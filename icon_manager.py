#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إدارة الأيقونات الموحدة لجميع نوافذ النظام
"""

import os
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import QSize

class IconManager:
    """مدير الأيقونات الموحدة للنظام"""

    def __init__(self):
        self.app_icon = None
        self._initialized = False
    
    def setup_icons(self):
        """إعداد الأيقونات"""
        # قائمة بمسارات الأيقونات المحتملة
        icon_paths = [
            "assets/icon.png",
            "assets/logo.png", 
            "assets/app_icon.png",
            "icon.png",
            "logo.png"
        ]
        
        # محاولة العثور على أيقونة موجودة
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    self.app_icon = QIcon(icon_path)
                    print(f"تم تحميل الأيقونة من: {icon_path}")
                    return
                except Exception as e:
                    print(f"فشل في تحميل الأيقونة من {icon_path}: {e}")
                    continue
        
        # إذا لم توجد أيقونة، إنشاء أيقونة افتراضية
        self.create_default_icon()
    
    def create_default_icon(self):
        """إنشاء أيقونة افتراضية"""
        try:
            # إنشاء أيقونة نصية بسيطة
            pixmap = QPixmap(64, 64)
            pixmap.fill()  # خلفية شفافة
            
            from PyQt5.QtGui import QPainter, QFont, QColor
            from PyQt5.QtCore import Qt
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # رسم دائرة خلفية
            painter.setBrush(QColor(102, 126, 234))  # لون أزرق
            painter.setPen(QColor(118, 75, 162))     # حدود بنفسجية
            painter.drawEllipse(4, 4, 56, 56)
            
            # إضافة نص
            painter.setPen(QColor(255, 255, 255))    # نص أبيض
            font = QFont("Arial", 20, QFont.Bold)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "F")
            
            painter.end()
            
            self.app_icon = QIcon(pixmap)
            print("تم إنشاء أيقونة افتراضية")
            
        except Exception as e:
            print(f"فشل في إنشاء الأيقونة الافتراضية: {e}")
            self.app_icon = QIcon()  # أيقونة فارغة
    
    def get_app_icon(self):
        """الحصول على أيقونة التطبيق"""
        return self.app_icon if self.app_icon else QIcon()
    
    def apply_to_window(self, window):
        """تطبيق الأيقونة على نافذة"""
        if self.app_icon and not self.app_icon.isNull():
            window.setWindowIcon(self.app_icon)
    
    def apply_to_message_box(self, message_box):
        """تطبيق الأيقونة على نافذة رسالة"""
        if self.app_icon and not self.app_icon.isNull():
            message_box.setWindowIcon(self.app_icon)

# إنشاء مثيل عام لمدير الأيقونات
icon_manager = IconManager()

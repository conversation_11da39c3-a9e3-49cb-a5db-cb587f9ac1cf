🎯 نظام Flex USA المحاسبي - معلومات المشروع
================================================================

📋 نظرة عامة:
نظام محاسبة مالي شامل مصمم خصيصاً للمحلات التي تشتري البضائع من الإنترنت
وتتعامل بعملتي الدينار الليبي والدولار الأمريكي.

🗂️ ملفات المشروع:
================================================================

📁 الملفات الأساسية:
- main.py                 : الملف الرئيسي لتشغيل التطبيق
- start.py                : ملف التشغيل المبسط مع فحص المتطلبات
- database.py             : إدارة قاعدة البيانات SQLite
- main_window.py          : النافذة الرئيسية للتطبيق
- transaction_dialog.py   : نوافذ إدارة العمليات المالية
- reports.py              : مولد التقارير (PDF & Excel)
- backup_manager.py       : إدارة النسخ الاحتياطية
- config.py               : إعدادات التطبيق

📁 ملفات الإعداد:
- requirements.txt        : قائمة المكتبات المطلوبة
- setup.py               : ملف إعداد التثبيت
- setup_demo_data.py     : إعداد البيانات التجريبية

📁 ملفات التشغيل:
- run_flex_usa.bat       : ملف تشغيل Windows (مفصل)
- تشغيل_سريع.bat         : ملف تشغيل سريع
- README.md              : دليل المشروع الشامل
- دليل_الاستخدام.md      : دليل الاستخدام المفصل

📁 المجلدات:
- backups/               : النسخ الاحتياطية
- reports/               : التقارير المُصدرة
- temp/                  : الملفات المؤقتة

🔧 المتطلبات التقنية:
================================================================
- Python 3.7 أو أحدث
- PyQt5 (واجهة المستخدم)
- SQLite (قاعدة البيانات)
- ReportLab (تقارير PDF)
- OpenPyXL (تقارير Excel)
- Requests (طلبات HTTP)
- Python-dateutil (معالجة التواريخ)

🚀 طرق التشغيل:
================================================================

1️⃣ التشغيل السريع:
   - انقر مرتين على "تشغيل_سريع.bat"

2️⃣ التشغيل المفصل:
   - انقر مرتين على "run_flex_usa.bat"

3️⃣ التشغيل اليدوي:
   - افتح موجه الأوامر
   - اكتب: python start.py

4️⃣ التشغيل المباشر:
   - افتح موجه الأوامر
   - اكتب: python main.py

🔑 بيانات الدخول:
================================================================
👤 المدير الرئيسي:
   اسم المستخدم: admin
   كلمة المرور: admin123

👤 حسابات تجريبية (إذا تم إعداد البيانات التجريبية):
   الكاشير: cashier1 / cash123
   مدخل البيانات: data_entry1 / data123
   مدير إضافي: manager1 / mgr123

💡 المميزات الرئيسية:
================================================================
✅ إدارة العمليات المالية (استلام/تسليم)
✅ دعم عملتي الدينار الليبي والدولار الأمريكي
✅ إدارة أسعار الصرف
✅ لوحة تحكم تفاعلية
✅ تقارير يومية وشهرية
✅ تصدير التقارير (PDF & Excel)
✅ نظام النسخ الاحتياطية
✅ إدارة المستخدمين والصلاحيات
✅ واجهة باللغة العربية

📊 البيانات التجريبية:
================================================================
إذا اخترت إعداد البيانات التجريبية، ستحصل على:
- 100 عملية مالية متنوعة
- أسعار صرف تاريخية
- حسابات مستخدمين متعددة
- أرصدة وإحصائيات واقعية

🛠️ استكشاف الأخطاء:
================================================================

❌ التطبيق لا يفتح:
   - تأكد من تثبيت Python
   - شغل: pip install -r requirements.txt
   - استخدم start.py للتشغيل

❌ خطأ في قاعدة البيانات:
   - احذف ملف flex_usa.db
   - شغل التطبيق مرة أخرى

❌ مشاكل في التقارير:
   - تأكد من وجود مجلد reports
   - تحقق من صلاحيات الكتابة

❌ مشاكل في النسخ الاحتياطية:
   - تأكد من وجود مجلد backups
   - تحقق من مساحة القرص الصلب

📞 الدعم والمساعدة:
================================================================
- راجع ملف README.md للمعلومات التفصيلية
- راجع ملف دليل_الاستخدام.md للتعليمات
- تحقق من رسائل الخطأ في موجه الأوامر
- تأكد من تثبيت جميع المتطلبات

🎉 ملاحظات مهمة:
================================================================
- غيّر كلمة مرور المدير بعد أول تسجيل دخول
- قم بعمل نسخة احتياطية دورية
- احتفظ بنسخة من قاعدة البيانات في مكان آمن
- راجع التقارير بانتظام للتأكد من دقة البيانات

================================================================
🏆 نظام Flex USA المحاسبي - الإصدار 1.0.0
💻 تم التطوير باستخدام Python & PyQt5
📅 تاريخ الإنشاء: 2024
================================================================

شكراً لاستخدام نظام Flex USA المحاسبي! 🎯

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التصميم الموحد والاحترافي لنظام Flex USA
يتضمن الألوان، الخطوط، الأحجام، والأنماط المتسقة
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPalette

class ModernTheme:
    """نظام التصميم الموحد والاحترافي"""
    
    # 🎨 نظام الألوان الأساسي
    BACKGROUND_LIGHT = "#F4F6F9"      # الخلفية العامة - رمادي فاتح جداً
    HEADER_DARK = "#2C3E50"           # رؤوس النوافذ - أزرق داكن أنيق
    PRIMARY_BLUE = "#3498DB"          # الأزرار الرئيسية - أزرق متوسط
    SECONDARY_GRAY = "#95A5A6"        # الأزرار الثانوية - رم<PERSON>ي متوسط
    TEXT_PRIMARY = "#2D3436"          # النصوص الأساسية - أسود فاتح
    TEXT_SECONDARY = "#7F8C8D"        # النصوص التوضيحية - رمادي داكن
    WARNING_RED = "#E74C3C"           # اللون التحذيري - أحمر ناعم
    SUCCESS_GREEN = "#27AE60"         # لون النجاح - أخضر هادئ
    WHITE = "#FFFFFF"                 # أبيض نقي
    CARD_BACKGROUND = "#FFFFFF"       # خلفية البطاقات
    BORDER_LIGHT = "#E1E8ED"          # حدود فاتحة
    SHADOW_COLOR = "rgba(0,0,0,0.1)"  # لون الظل
    
    # 🎨 ألوان إضافية للتدرجات
    PRIMARY_HOVER = "#2980B9"         # أزرق أغمق للتفاعل
    SUCCESS_HOVER = "#229954"         # أخضر أغمق للتفاعل
    WARNING_HOVER = "#C0392B"         # أحمر أغمق للتفاعل
    
    # 🅰️ نظام الخطوط
    FONT_FAMILY = "Cairo, Segoe UI, Tahoma, Arial, sans-serif"
    FONT_SIZE_LARGE = 18              # العناوين الكبيرة
    FONT_SIZE_HEADER = 16             # العناوين الفرعية
    FONT_SIZE_NORMAL = 12             # النصوص العادية
    FONT_SIZE_SMALL = 10              # الملاحظات والتلميحات
    
    # 📏 الأحجام والمسافات
    BORDER_RADIUS = 6                 # زوايا دائرية
    PADDING_SMALL = 8                 # مسافة صغيرة
    PADDING_MEDIUM = 16               # مسافة متوسطة
    PADDING_LARGE = 24                # مسافة كبيرة
    
    # 🔘 أحجام الأزرار
    BUTTON_HEIGHT_LARGE = 40          # زر كبير
    BUTTON_HEIGHT_MEDIUM = 35         # زر متوسط
    BUTTON_HEIGHT_SMALL = 30          # زر صغير
    BUTTON_WIDTH_LARGE = 200          # عرض زر كبير
    BUTTON_WIDTH_MEDIUM = 150         # عرض زر متوسط
    BUTTON_WIDTH_SMALL = 100          # عرض زر صغير
    
    @staticmethod
    def get_main_stylesheet():
        """الحصول على الستايل الرئيسي للتطبيق"""
        return f"""
        /* 🎨 الستايل العام للتطبيق */
        QApplication {{
            font-family: {ModernTheme.FONT_FAMILY};
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            color: {ModernTheme.TEXT_PRIMARY};
            background-color: {ModernTheme.BACKGROUND_LIGHT};
        }}
        
        /* 🪟 النوافذ الرئيسية */
        QMainWindow {{
            background-color: {ModernTheme.BACKGROUND_LIGHT};
            color: {ModernTheme.TEXT_PRIMARY};
        }}
        
        QDialog {{
            background-color: {ModernTheme.WHITE};
            color: {ModernTheme.TEXT_PRIMARY};
            border: 1px solid {ModernTheme.BORDER_LIGHT};
            border-radius: {ModernTheme.BORDER_RADIUS}px;
        }}
        
        /* 📝 حقول الإدخال */
        QLineEdit {{
            background-color: {ModernTheme.WHITE};
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: {ModernTheme.BORDER_RADIUS}px;
            padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            color: {ModernTheme.TEXT_PRIMARY};
            selection-background-color: {ModernTheme.PRIMARY_BLUE};
        }}
        
        QLineEdit:focus {{
            border-color: {ModernTheme.PRIMARY_BLUE};
        }}
        
        QLineEdit:hover {{
            border-color: {ModernTheme.PRIMARY_HOVER};
        }}
        
        /* 🔘 الأزرار الرئيسية */
        QPushButton {{
            background-color: {ModernTheme.PRIMARY_BLUE};
            color: {ModernTheme.WHITE};
            border: none;
            border-radius: {ModernTheme.BORDER_RADIUS}px;
            padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            font-weight: 600;
            min-height: {ModernTheme.BUTTON_HEIGHT_MEDIUM}px;
            min-width: {ModernTheme.BUTTON_WIDTH_MEDIUM}px;
        }}
        
        QPushButton:hover {{
            background-color: {ModernTheme.PRIMARY_HOVER};
        }}

        QPushButton:pressed {{
            background-color: {ModernTheme.PRIMARY_HOVER};
        }}
        
        QPushButton:disabled {{
            background-color: {ModernTheme.SECONDARY_GRAY};
            color: {ModernTheme.TEXT_SECONDARY};
        }}
        
        /* 🔘 الأزرار الثانوية */
        QPushButton[class="secondary"] {{
            background-color: {ModernTheme.SECONDARY_GRAY};
            color: {ModernTheme.WHITE};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: #7F8C8D;
        }}
        
        /* 🔘 أزرار النجاح */
        QPushButton[class="success"] {{
            background-color: {ModernTheme.SUCCESS_GREEN};
            color: {ModernTheme.WHITE};
        }}
        
        QPushButton[class="success"]:hover {{
            background-color: {ModernTheme.SUCCESS_HOVER};
        }}
        
        /* 🔘 أزرار التحذير */
        QPushButton[class="warning"] {{
            background-color: {ModernTheme.WARNING_RED};
            color: {ModernTheme.WHITE};
        }}
        
        QPushButton[class="warning"]:hover {{
            background-color: {ModernTheme.WARNING_HOVER};
        }}
        
        /* 📊 الجداول */
        QTableWidget {{
            background-color: {ModernTheme.WHITE};
            alternate-background-color: {ModernTheme.BACKGROUND_LIGHT};
            border: 1px solid {ModernTheme.BORDER_LIGHT};
            border-radius: {ModernTheme.BORDER_RADIUS}px;
            gridline-color: {ModernTheme.BORDER_LIGHT};
            selection-background-color: {ModernTheme.PRIMARY_BLUE};
            selection-color: {ModernTheme.WHITE};
        }}
        
        QHeaderView::section {{
            background-color: {ModernTheme.HEADER_DARK};
            color: {ModernTheme.WHITE};
            padding: {ModernTheme.PADDING_SMALL}px;
            border: none;
            font-weight: 600;
        }}
        
        /* 🏷️ التسميات */
        QLabel {{
            color: {ModernTheme.TEXT_PRIMARY};
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
        }}
        
        QLabel[class="header"] {{
            font-size: {ModernTheme.FONT_SIZE_HEADER}px;
            font-weight: 700;
            color: {ModernTheme.HEADER_DARK};
        }}
        
        QLabel[class="title"] {{
            font-size: {ModernTheme.FONT_SIZE_LARGE}px;
            font-weight: 700;
            color: {ModernTheme.HEADER_DARK};
        }}
        
        QLabel[class="subtitle"] {{
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            color: {ModernTheme.TEXT_SECONDARY};
        }}
        
        /* 📋 القوائم المنسدلة */
        QComboBox {{
            background-color: {ModernTheme.WHITE};
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: {ModernTheme.BORDER_RADIUS}px;
            padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
            font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            min-height: 30px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernTheme.PRIMARY_BLUE};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernTheme.TEXT_SECONDARY};
        }}
        
        /* 📱 شريط الحالة */
        QStatusBar {{
            background-color: {ModernTheme.HEADER_DARK};
            color: {ModernTheme.WHITE};
            border-top: 1px solid {ModernTheme.BORDER_LIGHT};
        }}
        
        /* 🎯 شريط التقدم */
        QProgressBar {{
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: {ModernTheme.BORDER_RADIUS}px;
            text-align: center;
            background-color: {ModernTheme.BACKGROUND_LIGHT};
        }}
        
        QProgressBar::chunk {{
            background-color: {ModernTheme.PRIMARY_BLUE};
            border-radius: {ModernTheme.BORDER_RADIUS - 2}px;
        }}
        """
    
    @staticmethod
    def get_login_stylesheet():
        """ستايل خاص بشاشة تسجيل الدخول"""
        return f"""
        QDialog {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.BACKGROUND_LIGHT},
                stop:1 {ModernTheme.WHITE});
            border: none;
        }}
        
        QLabel[class="login-title"] {{
            font-size: 24px;
            font-weight: 700;
            color: {ModernTheme.HEADER_DARK};
            margin-bottom: 10px;
        }}
        
        QLabel[class="login-subtitle"] {{
            font-size: 14px;
            color: {ModernTheme.TEXT_SECONDARY};
            margin-bottom: 30px;
        }}
        
        QLineEdit[class="login-input"] {{
            min-height: 45px;
            font-size: 14px;
            padding: 12px 16px;
            margin-bottom: 15px;
        }}
        
        QPushButton[class="login-button"] {{
            min-height: 45px;
            font-size: 16px;
            font-weight: 700;
            margin-top: 20px;
        }}
        """
    
    @staticmethod
    def apply_fonts(app):
        """تطبيق الخطوط على التطبيق"""
        # خط عام للتطبيق
        font = QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_NORMAL)
        app.setFont(font)
        
        return {
            'title': QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_LARGE, QFont.Bold),
            'header': QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_HEADER, QFont.DemiBold),
            'normal': QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_NORMAL),
            'small': QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_SMALL)
        }

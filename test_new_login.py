#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة تسجيل الدخول الجديدة حسب التصميم المطلوب
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from login_dialog import ProfessionalLoginDialog

def test_new_login_design():
    """اختبار التصميم الجديد لشاشة تسجيل الدخول"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء وعرض نافذة تسجيل الدخول الجديدة
    login_dialog = ProfessionalLoginDialog()
    
    print("🎯 تم تشغيل شاشة تسجيل الدخول الجديدة")
    print("📝 التصميم الجديد يتضمن:")
    print("   • شعار FlexUsa بألوان مميزة")
    print("   • خلفية داكنة احترافية")
    print("   • حقول إدخال شفافة")
    print("   • أزرار ملونة (أخضر للدخول، أحمر للإلغاء)")
    print("   • دعم كامل للغة العربية RTL")
    print("\n🔑 بيانات الاختبار:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    if login_dialog.exec_() == login_dialog.Accepted:
        print(f"\n✅ تم تسجيل الدخول بنجاح!")
        print(f"👤 اسم المستخدم: {login_dialog.username_input.text()}")
        print(f"🔒 كلمة المرور: {'*' * len(login_dialog.password_input.text())}")
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
    
    sys.exit(0)

if __name__ == "__main__":
    test_new_login_design()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قائمة إدارة المستخدمين والصلاحيات الجديدة
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_users_menu():
    """اختبار قائمة إدارة المستخدمين"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    print("🎯 تم إضافة خيار 'إدارة المستخدمين والصلاحيات' في قائمة الملف")
    print("📍 الموقع: قائمة الملف > تحت النسخة الاحتياطية")
    print("🔧 الميزات المتاحة:")
    print("   • إضافة مستخدمين جدد")
    print("   • تحديد الصلاحيات (مدير، كاشير، مدخل بيانات)")
    print("   • عرض قائمة المستخدمين الحاليين")
    print("   • تعديل وحذف المستخدمين")
    print("   • واجهة عربية RTL كاملة")
    print("\n🔑 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. اذهب إلى قائمة 'ملف'")
    print("   3. اختر '👥 إدارة المستخدمين والصلاحيات'")
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - يمكنك الآن اختبار الميزة الجديدة!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_users_menu()

# نظام Flex USA المحاسبي

## نظرة عامة
نظام محاسبة مالي شامل مصمم خصيصاً للمحلات التي تشتري البضائع من الإنترنت وتتعامل بعملتي الدينار الليبي والدولار الأمريكي.

## المميزات الرئيسية

### 🔐 إدارة المستخدمين
- **شاشة تسجيل دخول احترافية** مع تصميم عصري وتأثيرات بصرية
- تسجيل دخول آمن مع تشفير كلمات المرور
- صلاحيات متعددة (مدير، كاشير، مدخل بيانات)
- إدارة حسابات المستخدمين مع رسائل ترحيب مخصصة
- خيار "تذكر كلمة المرور" للراحة

### 💰 إدارة العمليات المالية
- تسجيل عمليات الاستلام والتسليم
- دعم العملتين: الدينار الليبي والدولار الأمريكي
- تتبع أسعار الصرف
- ربط العمليات بالجهات والمراجع

### 📊 لوحة التحكم
- عرض الأرصدة الحالية بالعملتين
- إحصائيات سريعة للعمليات
- متابعة المبالغ المستلمة والمسلمة

### 📈 التقارير
- تقارير يومية وشهرية
- تقارير حسب العملة
- تصدير بصيغ PDF و Excel
- إحصائيات الربح والخسارة

### 🔄 النسخ الاحتياطية
- إنشاء نسخ احتياطية تلقائية
- استرجاع البيانات
- إدارة النسخ الاحتياطية

## متطلبات التشغيل

### البرامج المطلوبة
- Python 3.7 أو أحدث
- PyQt5
- SQLite3

### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

## التثبيت والتشغيل

### 1. التشغيل السريع (الموصى به)
```bash
# Windows
تشغيل.bat

# أو مباشرة
python run.py
```

### 2. تثبيت المتطلبات (إذا لزم الأمر)
```bash
pip install -r requirements.txt
```

## بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع

```
flex-usa-accounting/
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── start.py                # ملف التشغيل المبسط مع فحص المتطلبات
├── main_window.py          # النافذة الرئيسية
├── login_dialog.py         # شاشة تسجيل الدخول الاحترافية
├── database.py             # إدارة قاعدة البيانات
├── transaction_dialog.py   # نوافذ العمليات المالية
├── reports.py              # مولد التقارير
├── backup_manager.py       # إدارة النسخ الاحتياطية
├── styles.py               # أنماط CSS للواجهات
├── config.py               # إعدادات التطبيق
├── setup_demo_data.py      # إعداد البيانات التجريبية
├── test_login.py           # اختبار شاشة تسجيل الدخول
├── requirements.txt        # المكتبات المطلوبة
├── تشغيل_سريع.bat          # ملف تشغيل سريع
├── run_flex_usa.bat        # ملف تشغيل مفصل
├── README.md              # دليل الاستخدام
├── دليل_الاستخدام.md      # دليل الاستخدام باللغة العربية
├── معلومات_المشروع.txt     # معلومات شاملة عن المشروع
├── backups/               # مجلد النسخ الاحتياطية
└── reports/               # مجلد التقارير المُصدرة
```

## الاستخدام

### إضافة عملية مالية جديدة
1. اضغط على "عملية جديدة" من شريط الأدوات
2. اختر نوع العملية (استلام/تسليم)
3. حدد العملة والمبلغ
4. أدخل بيانات الجهة والمرجع
5. اضغط "حفظ"

### إنشاء التقارير
1. من قائمة "التقارير" اختر نوع التقرير المطلوب
2. حدد نوع التصدير (PDF أو Excel)
3. سيتم حفظ التقرير في مجلد "reports"

### إدارة النسخ الاحتياطية
1. من قائمة "ملف" اختر "نسخة احتياطية"
2. يمكنك إنشاء، استرجاع، أو حذف النسخ الاحتياطية

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل تقديم أي تعديلات.

---

**Flex USA - نظام المحاسبة المالي**  
الإصدار 1.0.0

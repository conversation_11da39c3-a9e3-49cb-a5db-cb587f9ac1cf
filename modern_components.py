#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مكونات واجهة المستخدم الاحترافية القابلة لإعادة الاستخدام
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme

class ModernButton(QPushButton):
    """زر احترافي مع أنماط متعددة"""
    
    def __init__(self, text="", button_type="primary", size="medium", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.size = size
        self.setup_button()
    
    def setup_button(self):
        """إعداد الزر"""
        # تحديد الحجم
        if self.size == "large":
            self.setMinimumSize(ModernTheme.BUTTON_WIDTH_LARGE, ModernTheme.BUTTON_HEIGHT_LARGE)
        elif self.size == "small":
            self.setMinimumSize(ModernTheme.BUTTON_WIDTH_SMALL, ModernTheme.BUTTON_HEIGHT_SMALL)
        else:  # medium
            self.setMinimumSize(ModernTheme.BUTTON_WIDTH_MEDIUM, ModernTheme.BUTTON_HEIGHT_MEDIUM)
        
        # تطبيق النمط
        self.setProperty("class", self.button_type)
        self.setCursor(Qt.PointingHandCursor)

class ModernCard(QFrame):
    """بطاقة احترافية مع ظل"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_card()
    
    def setup_card(self):
        """إعداد البطاقة"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.CARD_BACKGROUND};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_MEDIUM}px;
            }}
        """)
        
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)

class ModernInputField(QLineEdit):
    """حقل إدخال احترافي مع أيقونة"""
    
    def __init__(self, placeholder="", icon=None, parent=None):
        super().__init__(parent)
        self.placeholder = placeholder
        self.icon = icon
        self.setup_field()
    
    def setup_field(self):
        """إعداد حقل الإدخال"""
        self.setPlaceholderText(self.placeholder)
        
        if self.icon:
            # إضافة أيقونة داخل الحقل
            self.setTextMargins(35, 0, 0, 0)
            
            # إنشاء تسمية للأيقونة
            icon_label = QLabel(self)
            icon_label.setPixmap(self.icon.pixmap(20, 20))
            icon_label.setGeometry(10, 10, 20, 20)
            icon_label.setStyleSheet("background: transparent;")

class ModernComboBox(QComboBox):
    """قائمة منسدلة احترافية"""
    
    def __init__(self, items=None, parent=None):
        super().__init__(parent)
        if items:
            self.addItems(items)
        self.setup_combo()
    
    def setup_combo(self):
        """إعداد القائمة المنسدلة"""
        self.setStyleSheet(f"""
            QComboBox {{
                background-color: {ModernTheme.WHITE};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
                min-height: 35px;
            }}
            
            QComboBox:focus {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {ModernTheme.TEXT_SECONDARY};
            }}
            
            QComboBox QAbstractItemView {{
                background-color: {ModernTheme.WHITE};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                selection-background-color: {ModernTheme.PRIMARY_BLUE};
                selection-color: {ModernTheme.WHITE};
            }}
        """)

class ModernTable(QTableWidget):
    """جدول احترافي مع تنسيق متقدم"""
    
    def __init__(self, rows=0, columns=0, parent=None):
        super().__init__(rows, columns, parent)
        self.setup_table()
    
    def setup_table(self):
        """إعداد الجدول"""
        # إعدادات عامة
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setSortingEnabled(True)
        
        # إعداد الرأس
        header = self.horizontalHeader()
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setStretchLastSection(True)
        
        # إعداد الرأس العمودي
        v_header = self.verticalHeader()
        v_header.setVisible(False)
        
        # تطبيق الستايل
        self.setStyleSheet(f"""
            QTableWidget {{
                background-color: {ModernTheme.WHITE};
                alternate-background-color: {ModernTheme.BACKGROUND_LIGHT};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                gridline-color: {ModernTheme.BORDER_LIGHT};
                selection-background-color: {ModernTheme.PRIMARY_BLUE};
                selection-color: {ModernTheme.WHITE};
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            }}
            
            QHeaderView::section {{
                background-color: {ModernTheme.HEADER_DARK};
                color: {ModernTheme.WHITE};
                padding: {ModernTheme.PADDING_SMALL}px;
                border: none;
                font-weight: 600;
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            }}
            
            QTableWidget::item {{
                padding: {ModernTheme.PADDING_SMALL}px;
                border-bottom: 1px solid {ModernTheme.BORDER_LIGHT};
            }}
            
            QTableWidget::item:selected {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                color: {ModernTheme.WHITE};
            }}
        """)

class ModernDialog(QDialog):
    """نافذة حوار احترافية"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setup_dialog()
    
    def setup_dialog(self):
        """إعداد النافذة"""
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        
        # تطبيق الستايل
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {ModernTheme.WHITE};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
            }}
        """)
        
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)

class ModernLabel(QLabel):
    """تسمية احترافية مع أنماط متعددة"""
    
    def __init__(self, text="", label_type="normal", parent=None):
        super().__init__(text, parent)
        self.label_type = label_type
        self.setup_label()
    
    def setup_label(self):
        """إعداد التسمية"""
        self.setProperty("class", self.label_type)
        
        if self.label_type == "title":
            font = QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_LARGE, QFont.Bold)
            self.setFont(font)
        elif self.label_type == "header":
            font = QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_HEADER, QFont.DemiBold)
            self.setFont(font)
        elif self.label_type == "subtitle":
            font = QFont(ModernTheme.FONT_FAMILY.split(',')[0], ModernTheme.FONT_SIZE_NORMAL)
            self.setFont(font)

class ModernProgressBar(QProgressBar):
    """شريط تقدم احترافي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_progress()
    
    def setup_progress(self):
        """إعداد شريط التقدم"""
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                text-align: center;
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                color: {ModernTheme.TEXT_PRIMARY};
                font-weight: 600;
            }}
            
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:1 {ModernTheme.PRIMARY_HOVER});
                border-radius: {ModernTheme.BORDER_RADIUS - 2}px;
            }}
        """)

class ModernMessageBox:
    """رسائل تنبيه احترافية"""
    
    @staticmethod
    def show_info(parent, title, message):
        """عرض رسالة معلومات"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setStyleSheet(ModernMessageBox.get_style())
        return msg.exec_()
    
    @staticmethod
    def show_warning(parent, title, message):
        """عرض رسالة تحذير"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Warning)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setStyleSheet(ModernMessageBox.get_style())
        return msg.exec_()
    
    @staticmethod
    def show_error(parent, title, message):
        """عرض رسالة خطأ"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Critical)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setStyleSheet(ModernMessageBox.get_style())
        return msg.exec_()
    
    @staticmethod
    def show_question(parent, title, message):
        """عرض سؤال تأكيد"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Question)
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.No)
        msg.setStyleSheet(ModernMessageBox.get_style())
        return msg.exec_()
    
    @staticmethod
    def get_style():
        """الحصول على ستايل الرسائل"""
        return f"""
            QMessageBox {{
                background-color: {ModernTheme.WHITE};
                color: {ModernTheme.TEXT_PRIMARY};
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
            }}
            
            QMessageBox QPushButton {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                color: {ModernTheme.WHITE};
                border: none;
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
                font-weight: 600;
                min-width: 80px;
                min-height: 30px;
            }}
            
            QMessageBox QPushButton:hover {{
                background-color: {ModernTheme.PRIMARY_HOVER};
            }}
        """

class ModernLoginDialog(QDialog):
    """شاشة تسجيل دخول احترافية وعصرية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_login_dialog()

    def setup_login_dialog(self):
        """إعداد شاشة تسجيل الدخول"""
        self.setWindowTitle("🔐 تسجيل الدخول - Flex USA")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.Dialog | Qt.MSWindowsFixedSizeDialogHint)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الستايل
        self.setStyleSheet(ModernTheme.get_login_stylesheet())

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(20)

        # إضافة المحتوى
        self.create_header(main_layout)
        self.create_form(main_layout)
        self.create_footer(main_layout)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.connect_events()

        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()

    def create_header(self, layout):
        """إنشاء رأس الشاشة"""
        # شعار النظام
        logo_label = ModernLabel("💼", "title")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet(f"font-size: 48px; color: {ModernTheme.PRIMARY_BLUE}; margin-bottom: 10px;")
        layout.addWidget(logo_label)

        # عنوان النظام
        title_label = ModernLabel("نظام Flex USA المحاسبي", "title")
        title_label.setProperty("class", "login-title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # العنوان الفرعي
        subtitle_label = ModernLabel("مرحباً بك، يرجى تسجيل الدخول للمتابعة", "subtitle")
        subtitle_label.setProperty("class", "login-subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle_label)

    def create_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        # بطاقة النموذج
        form_card = ModernCard()
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)

        # حقل اسم المستخدم
        username_label = ModernLabel("اسم المستخدم:", "normal")
        self.username_input = ModernInputField("أدخل اسم المستخدم")
        self.username_input.setProperty("class", "login-input")

        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)

        # حقل كلمة المرور
        password_label = ModernLabel("كلمة المرور:", "normal")
        self.password_input = ModernInputField("أدخل كلمة المرور")
        self.password_input.setProperty("class", "login-input")
        self.password_input.setEchoMode(QLineEdit.Password)

        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)

        # أزرار العمل
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        self.cancel_button = ModernButton("إلغاء", "secondary", "medium")
        self.login_button = ModernButton("تسجيل الدخول", "primary", "medium")
        self.login_button.setProperty("class", "login-button")

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)

        form_layout.addLayout(buttons_layout)

        form_card.setLayout(form_layout)
        layout.addWidget(form_card)

    def create_footer(self, layout):
        """إنشاء تذييل الشاشة"""
        # معلومات تسجيل الدخول التجريبية
        info_label = ModernLabel("معلومات تسجيل الدخول التجريبية:", "subtitle")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        demo_info = ModernLabel("اسم المستخدم: admin | كلمة المرور: admin123", "small")
        demo_info.setAlignment(Qt.AlignCenter)
        demo_info.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-style: italic;")
        layout.addWidget(demo_info)

        # حقوق الطبع
        copyright_label = ModernLabel("© 2024 Flex USA - جميع الحقوق محفوظة", "small")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; margin-top: 20px;")
        layout.addWidget(copyright_label)

    def connect_events(self):
        """ربط الأحداث"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.textChanged.connect(self.validate_inputs)
        self.password_input.textChanged.connect(self.validate_inputs)

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # تفعيل/تعطيل زر تسجيل الدخول
        self.login_button.setEnabled(bool(username and password))

    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            ModernMessageBox.show_warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # محاولة تسجيل الدخول
        try:
            from database import DatabaseManager
            db = DatabaseManager()
            user = db.authenticate_user(username, password)

            if user:
                self.user_data = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }
                ModernMessageBox.show_info(self, "نجح تسجيل الدخول", f"مرحباً {user[1]}!")
                self.accept()
            else:
                ModernMessageBox.show_error(self, "خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()

        except Exception as e:
            ModernMessageBox.show_error(self, "خطأ في النظام", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def get_user_data(self):
        """الحصول على بيانات المستخدم"""
        return self.user_data

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول الجديدة مطابقة للتصميم المطلوب
تصميم بجانبين: أزرق وأبيض
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
from modern_components import *
from icon_manager import icon_manager

class ProfessionalLoginScreen(ModernDialog):
    """شاشة تسجيل دخول مطابقة للتصميم المطلوب"""
    
    def __init__(self, parent=None):
        super().__init__("تسجيل الدخول - Flex USA", parent)
        self.user_data = None
        self.setup_professional_login()
        
    def setup_professional_login(self):
        """إعداد شاشة تسجيل الدخول الجديدة"""
        # إعدادات النافذة
        self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)
        
        # إنشاء التخطيط الرئيسي الأفقي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الجانب الأزرق (يمين)
        blue_side = self.create_blue_side()
        main_layout.addWidget(blue_side)
        
        # إنشاء الجانب الأبيض (يسار) 
        white_side = self.create_white_side()
        main_layout.addWidget(white_side)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.connect_events()
        
        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(200, lambda: self.username_input.setFocus())
    
    def create_blue_side(self):
        """إنشاء الجانب الأزرق مع عنوان النظام"""
        blue_widget = QFrame()
        blue_widget.setFixedWidth(450)
        blue_widget.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4A90E2,
                    stop:0.5 #357ABD,
                    stop:1 #2E5F8A);
                border: none;
            }
        """)
        
        blue_layout = QVBoxLayout()
        blue_layout.setAlignment(Qt.AlignCenter)
        blue_layout.setContentsMargins(60, 80, 60, 80)
        blue_layout.setSpacing(30)
        
        # عنوان النظام الرئيسي
        main_title = ModernLabel("Flex USA", "title")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-size: 48px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
            }
        """)
        blue_layout.addWidget(main_title)
        
        # العنوان الفرعي
        subtitle = ModernLabel("Accounting System", "subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: rgba(255, 255, 255, 0.9);
                font-weight: 300;
            }
        """)
        blue_layout.addWidget(subtitle)
        
        blue_widget.setLayout(blue_layout)
        return blue_widget
    
    def create_white_side(self):
        """إنشاء الجانب الأبيض مع نموذج تسجيل الدخول"""
        white_widget = QFrame()
        white_widget.setFixedWidth(450)
        white_widget.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
            }
        """)
        
        white_layout = QVBoxLayout()
        white_layout.setAlignment(Qt.AlignCenter)
        white_layout.setContentsMargins(60, 80, 60, 80)
        white_layout.setSpacing(30)
        
        # عنوان تسجيل الدخول
        login_title = ModernLabel("تسجيل الدخول", "header")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                font-weight: bold;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: 10px;
            }}
        """)
        white_layout.addWidget(login_title)
        
        # النص التوضيحي
        welcome_text = ModernLabel("أهلاً بك، أدخل بياناتك للوصول إلى حسابك.", "normal")
        welcome_text.setAlignment(Qt.AlignCenter)
        welcome_text.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: 30px;
            }}
        """)
        white_layout.addWidget(welcome_text)
        
        # نموذج تسجيل الدخول
        form_widget = self.create_simple_form()
        white_layout.addWidget(form_widget)
        
        white_widget.setLayout(white_layout)
        return white_widget
    
    def create_simple_form(self):
        """إنشاء نموذج تسجيل الدخول البسيط"""
        form_widget = QFrame()
        form_widget.setStyleSheet("QFrame { background: transparent; border: none; }")
        
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(0, 0, 0, 0)
        
        # حقل اسم المستخدم
        username_label = ModernLabel("اسم المستخدم", "normal")
        username_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_PRIMARY};
                font-weight: 500;
                margin-bottom: 5px;
            }}
        """)
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("مثال: ahmad.ali")
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 15px;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 16px;
                background-color: white;
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: #4A90E2;
                outline: none;
            }}
        """)
        form_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = ModernLabel("كلمة المرور", "normal")
        password_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_PRIMARY};
                font-weight: 500;
                margin-bottom: 5px;
                margin-top: 10px;
            }}
        """)
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("••••••••••")
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 15px;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 16px;
                background-color: white;
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: #4A90E2;
                outline: none;
            }}
        """)
        form_layout.addWidget(self.password_input)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(0, 20, 0, 0)
        
        # زر الإلغاء
        self.cancel_button = ModernButton("إلغاء", "secondary")
        self.cancel_button.setFixedHeight(50)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                padding: 12px 30px;
            }}
            QPushButton:hover {{
                background-color: #F8F9FA;
                border-color: #D1D9E0;
            }}
        """)
        buttons_layout.addWidget(self.cancel_button)
        
        # زر تسجيل الدخول
        self.login_button = ModernButton("دخول", "primary")
        self.login_button.setFixedHeight(50)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 30px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E5F8A;
            }
        """)
        buttons_layout.addWidget(self.login_button)
        
        form_layout.addLayout(buttons_layout)
        
        form_widget.setLayout(form_layout)
        return form_widget
    
    def connect_events(self):
        """ربط الأحداث"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.close)
        
        # ربط Enter للتسجيل
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.handle_login)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # محاكاة عملية التحقق
        if username == "admin" and password == "admin":
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    login_screen = ProfessionalLoginScreen()
    if login_screen.exec_() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    sys.exit()

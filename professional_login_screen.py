#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول الاحترافية والجذابة لنظام Flex USA
تصميم متطور مع تكامل كامل مع مكونات النظام
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
from modern_components import *
from icon_manager import icon_manager

class ProfessionalLoginScreen(ModernDialog):
    """شاشة تسجيل دخول احترافية وجذابة"""
    
    def __init__(self, parent=None):
        super().__init__("🔐 تسجيل الدخول - Flex USA Accounting System", parent)
        self.user_data = None
        self.setup_professional_login()
        self.apply_smooth_effects()
    
    def setup_professional_login(self):
        """إعداد شاشة تسجيل الدخول الاحترافية"""
        # إعدادات النافذة
        self.setFixedSize(500, 650)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE, 
                                     ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE)
        main_layout.setSpacing(ModernTheme.PADDING_LARGE)
        
        # إنشاء رأس الشاشة
        header_widget = self.create_professional_header()
        main_layout.addWidget(header_widget)
        
        # إنشاء نموذج تسجيل الدخول
        form_widget = self.create_login_form()
        main_layout.addWidget(form_widget)
        
        # إنشاء أزرار العمل
        buttons_widget = self.create_action_buttons()
        main_layout.addWidget(buttons_widget)
        
        # إنشاء معلومات تجريبية
        demo_widget = self.create_demo_credentials()
        main_layout.addWidget(demo_widget)
        
        # مساحة مرنة
        main_layout.addStretch()
        
        # إنشاء تذييل الشاشة
        footer_widget = self.create_footer()
        main_layout.addWidget(footer_widget)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.connect_events()
        
        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(200, lambda: self.username_input.setFocus())
    
    def create_professional_header(self):
        """إنشاء رأس احترافي للشاشة"""
        header_card = QFrame()
        header_card.setFrameStyle(QFrame.NoFrame)
        header_card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:1 {ModernTheme.PRIMARY_HOVER});
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_LARGE}px;
            }}
        """)
        
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(ModernTheme.PADDING_MEDIUM)
        
        # شعار النظام
        logo_container = self.create_system_logo()
        header_layout.addWidget(logo_container)
        
        # عنوان النظام
        system_title = ModernLabel("نظام Flex USA", "title")
        system_title.setAlignment(Qt.AlignCenter)
        system_title.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_LARGE + 6}px;
                font-weight: bold;
                color: white;
                margin: {ModernTheme.PADDING_SMALL}px 0;
            }}
        """)
        header_layout.addWidget(system_title)
        
        # وصف النظام
        system_desc = ModernLabel("نظام المحاسبة المالي الاحترافي", "subtitle")
        system_desc.setAlignment(Qt.AlignCenter)
        system_desc.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 2}px;
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        header_layout.addWidget(system_desc)
        
        header_card.setLayout(header_layout)
        return header_card
    
    def create_system_logo(self):
        """إنشاء شعار النظام"""
        logo_container = QWidget()
        logo_layout = QVBoxLayout()
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # إطار الشعار
        logo_frame = QFrame()
        logo_frame.setFixedSize(100, 100)
        logo_frame.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.15);
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 50px;
            }}
        """)
        
        # أيقونة الشعار
        logo_icon = ModernLabel("💼", "title")
        logo_icon.setAlignment(Qt.AlignCenter)
        logo_icon.setStyleSheet("""
            QLabel {
                font-size: 45px;
                color: white;
                background: transparent;
                border: none;
            }
        """)
        
        frame_layout = QVBoxLayout()
        frame_layout.addWidget(logo_icon)
        frame_layout.setContentsMargins(0, 0, 0, 0)
        logo_frame.setLayout(frame_layout)
        
        logo_layout.addWidget(logo_frame)
        logo_container.setLayout(logo_layout)
        
        return logo_container
    
    def create_login_form(self):
        """إنشاء نموذج تسجيل الدخول"""
        form_card = QFrame()
        form_card.setFrameStyle(QFrame.NoFrame)
        form_card.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.WHITE};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_LARGE}px;
            }}
        """)
        
        form_layout = QVBoxLayout()
        form_layout.setSpacing(ModernTheme.PADDING_MEDIUM)
        
        # عنوان النموذج
        form_title = ModernLabel("تسجيل الدخول", "header")
        form_title.setAlignment(Qt.AlignCenter)
        form_title.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.HEADER_DARK};
                margin-bottom: {ModernTheme.PADDING_MEDIUM}px;
                font-weight: bold;
            }}
        """)
        form_layout.addWidget(form_title)
        
        # حقل اسم المستخدم
        username_section = self.create_username_field()
        form_layout.addWidget(username_section)
        
        # حقل كلمة المرور
        password_section = self.create_password_field()
        form_layout.addWidget(password_section)
        
        form_card.setLayout(form_layout)
        return form_card

    def create_username_field(self):
        """إنشاء حقل اسم المستخدم مع أيقونة"""
        username_container = QWidget()
        username_layout = QVBoxLayout()
        username_layout.setContentsMargins(0, 0, 0, 0)
        username_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # تسمية الحقل
        username_label = ModernLabel("👤 اسم المستخدم:", "normal")
        username_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 1}px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        username_layout.addWidget(username_label)

        # حقل الإدخال
        self.username_input = ModernInputField("أدخل اسم المستخدم")
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {ModernTheme.WHITE};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_MEDIUM}px;
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 1}px;
                color: {ModernTheme.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border-color: {ModernTheme.PRIMARY_BLUE};
                background-color: {ModernTheme.BACKGROUND_LIGHT};
            }}
            QLineEdit:hover {{
                border-color: {ModernTheme.PRIMARY_HOVER};
            }}
            QLineEdit::placeholder {{
                color: {ModernTheme.TEXT_SECONDARY};
                font-style: italic;
            }}
        """)
        username_layout.addWidget(self.username_input)

        username_container.setLayout(username_layout)
        return username_container

    def create_password_field(self):
        """إنشاء حقل كلمة المرور مع أيقونة القفل"""
        password_container = QWidget()
        password_layout = QVBoxLayout()
        password_layout.setContentsMargins(0, 0, 0, 0)
        password_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # تسمية الحقل
        password_label = ModernLabel("🔒 كلمة المرور:", "normal")
        password_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 1}px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        password_layout.addWidget(password_label)

        # حقل الإدخال
        self.password_input = ModernInputField("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {ModernTheme.WHITE};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_MEDIUM}px;
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 1}px;
                color: {ModernTheme.TEXT_PRIMARY};
                min-height: 20px;
            }}
            QLineEdit:focus {{
                border-color: {ModernTheme.PRIMARY_BLUE};
                background-color: {ModernTheme.BACKGROUND_LIGHT};
            }}
            QLineEdit:hover {{
                border-color: {ModernTheme.PRIMARY_HOVER};
            }}
            QLineEdit::placeholder {{
                color: {ModernTheme.TEXT_SECONDARY};
                font-style: italic;
            }}
        """)
        password_layout.addWidget(self.password_input)

        password_container.setLayout(password_layout)
        return password_container

    def create_action_buttons(self):
        """إنشاء أزرار العمل الاحترافية"""
        buttons_card = QFrame()
        buttons_card.setFrameStyle(QFrame.NoFrame)
        buttons_card.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.WHITE};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_LARGE}px;
            }}
        """)

        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(ModernTheme.PADDING_MEDIUM)

        # زر تسجيل الدخول الرئيسي
        self.login_button = ModernButton("🚀 تسجيل الدخول", "primary", "large")
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:1 {ModernTheme.PRIMARY_HOVER});
                color: white;
                border: none;
                border-radius: {ModernTheme.BORDER_RADIUS + 2}px;
                padding: {ModernTheme.PADDING_MEDIUM}px {ModernTheme.PADDING_LARGE}px;
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 2}px;
                font-weight: bold;
                min-height: 45px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.HEADER_DARK};
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.SECONDARY_GRAY};
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)
        buttons_layout.addWidget(self.login_button)

        # زر الإلغاء الثانوي
        self.cancel_button = ModernButton("❌ إلغاء", "secondary", "medium")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
                font-weight: 600;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                border-color: {ModernTheme.SECONDARY_GRAY};
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.SECONDARY_GRAY};
                color: white;
            }}
        """)
        buttons_layout.addWidget(self.cancel_button)

        buttons_card.setLayout(buttons_layout)
        return buttons_card

    def create_demo_credentials(self):
        """إنشاء معلومات تسجيل الدخول التجريبية"""
        demo_card = QFrame()
        demo_card.setFrameStyle(QFrame.NoFrame)
        demo_card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.SUCCESS_GREEN},
                    stop:1 {ModernTheme.SUCCESS_HOVER});
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                padding: {ModernTheme.PADDING_MEDIUM}px;
            }}
        """)

        demo_layout = QVBoxLayout()
        demo_layout.setAlignment(Qt.AlignCenter)
        demo_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # عنوان المعلومات التجريبية
        demo_title = ModernLabel("🔑 معلومات تسجيل الدخول التجريبية", "normal")
        demo_title.setAlignment(Qt.AlignCenter)
        demo_title.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: white;
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        demo_layout.addWidget(demo_title)

        # معلومات الدخول
        credentials_info = ModernLabel("اسم المستخدم: admin | كلمة المرور: admin123", "small")
        credentials_info.setAlignment(Qt.AlignCenter)
        credentials_info.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_SMALL + 1}px;
                color: rgba(255, 255, 255, 0.95);
                background: rgba(255, 255, 255, 0.1);
                padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
                border-radius: {ModernTheme.BORDER_RADIUS - 2}px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)
        demo_layout.addWidget(credentials_info)

        demo_card.setLayout(demo_layout)
        return demo_card

    def create_footer(self):
        """إنشاء تذييل الشاشة"""
        footer_widget = QWidget()
        footer_layout = QVBoxLayout()
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # معلومات الإصدار
        version_label = ModernLabel("الإصدار 1.0.0", "small")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.TEXT_SECONDARY};
                font-size: {ModernTheme.FONT_SIZE_SMALL}px;
            }}
        """)
        footer_layout.addWidget(version_label)

        # حقوق الطبع
        copyright_label = ModernLabel("© 2024 Flex USA - جميع الحقوق محفوظة", "small")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.TEXT_SECONDARY};
                font-size: {ModernTheme.FONT_SIZE_SMALL - 1}px;
            }}
        """)
        footer_layout.addWidget(copyright_label)

        footer_widget.setLayout(footer_layout)
        return footer_widget

    def apply_smooth_effects(self):
        """تطبيق التأثيرات السلسة"""
        # تأثير الظهور التدريجي
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)

        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutQuart)

        # بدء التأثير
        QTimer.singleShot(100, self.fade_animation.start)

    def connect_events(self):
        """ربط الأحداث والتفاعلات"""
        # ربط أزرار العمل
        self.login_button.clicked.connect(self.handle_login_process)

        # دعم الضغط على Enter
        self.password_input.returnPressed.connect(self.handle_login_process)
        self.username_input.returnPressed.connect(lambda: self.password_input.setFocus())

        # التحقق من صحة المدخلات
        self.username_input.textChanged.connect(self.validate_input_fields)
        self.password_input.textChanged.connect(self.validate_input_fields)

        # تأثيرات التفاعل
        self.username_input.focusInEvent = lambda _: self.apply_focus_effect(self.username_input, True)
        self.username_input.focusOutEvent = lambda _: self.apply_focus_effect(self.username_input, False)
        self.password_input.focusInEvent = lambda _: self.apply_focus_effect(self.password_input, True)
        self.password_input.focusOutEvent = lambda _: self.apply_focus_effect(self.password_input, False)

    def apply_focus_effect(self, field, has_focus):
        """تطبيق تأثير التركيز على الحقول"""
        if has_focus:
            field.setStyleSheet(field.styleSheet().replace(
                f"border: 2px solid {ModernTheme.BORDER_LIGHT}",
                f"border: 2px solid {ModernTheme.PRIMARY_BLUE}"
            ))
        else:
            field.setStyleSheet(field.styleSheet().replace(
                f"border: 2px solid {ModernTheme.PRIMARY_BLUE}",
                f"border: 2px solid {ModernTheme.BORDER_LIGHT}"
            ))

    def validate_input_fields(self):
        """التحقق من صحة المدخلات"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # تفعيل/تعطيل زر تسجيل الدخول
        is_valid = bool(username and password and len(username) >= 3 and len(password) >= 3)
        self.login_button.setEnabled(is_valid)

        # تغيير مظهر الزر حسب الحالة
        if is_valid:
            self.login_button.setStyleSheet(self.login_button.styleSheet().replace(
                f"background-color: {ModernTheme.SECONDARY_GRAY}",
                f"background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {ModernTheme.PRIMARY_BLUE}, stop:1 {ModernTheme.PRIMARY_HOVER})"
            ))

    def handle_login_process(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة المدخلات
        if not self.validate_credentials(username, password):
            return

        # تأثير التحميل
        original_text = self.login_button.text()
        self.login_button.setText("⏳ جاري التحقق...")
        self.login_button.setEnabled(False)

        # محاولة تسجيل الدخول
        QTimer.singleShot(1200, lambda: self.process_authentication(username, password, original_text))

    def validate_credentials(self, username, password):
        """التحقق من صحة بيانات الاعتماد"""
        if not username:
            ModernMessageBox.show_warning(self, "تحذير", "⚠️ يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return False

        if not password:
            ModernMessageBox.show_warning(self, "تحذير", "⚠️ يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return False

        if len(username) < 3:
            ModernMessageBox.show_warning(self, "تحذير", "⚠️ اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            self.username_input.setFocus()
            return False

        if len(password) < 3:
            ModernMessageBox.show_warning(self, "تحذير", "⚠️ كلمة المرور يجب أن تكون 3 أحرف على الأقل")
            self.password_input.setFocus()
            return False

        return True

    def process_authentication(self, username, password, original_text):
        """معالجة التحقق من الهوية"""
        try:
            # ربط مع قاعدة البيانات
            from database import DatabaseManager
            db = DatabaseManager()
            user = db.authenticate_user(username, password)

            if user:
                # نجح تسجيل الدخول
                self.user_data = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }

                # رسالة نجاح
                ModernMessageBox.show_info(self, "✅ نجح تسجيل الدخول",
                                         f"🎉 مرحباً بك {user[1]}!\nتم تسجيل الدخول بنجاح")

                # إغلاق النافذة بعد فترة قصيرة
                QTimer.singleShot(1500, self.accept)
            else:
                # فشل تسجيل الدخول
                ModernMessageBox.show_error(self, "❌ خطأ في تسجيل الدخول",
                                          "🚫 اسم المستخدم أو كلمة المرور غير صحيحة\nيرجى المحاولة مرة أخرى")

                # مسح كلمة المرور والتركيز عليها
                self.password_input.clear()
                self.password_input.setFocus()

        except Exception as e:
            # خطأ في النظام
            ModernMessageBox.show_error(self, "❌ خطأ في النظام",
                                      f"🔧 حدث خطأ أثناء تسجيل الدخول:\n{str(e)}\n\nيرجى المحاولة مرة أخرى")

        finally:
            # استعادة حالة الزر
            self.login_button.setText(original_text)
            self.login_button.setEnabled(True)

    def get_user_data(self):
        """الحصول على بيانات المستخدم المسجل"""
        return self.user_data

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.login_button.isEnabled():
                self.handle_login_process()
        else:
            super().keyPressEvent(event)

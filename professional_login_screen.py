#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول الجديدة مطابقة للتصميم المطلوب
تصميم بجانبين: أزرق وأبيض
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
from modern_components import *
from icon_manager import icon_manager

class ProfessionalLoginScreen(ModernDialog):
    """شاشة تسجيل دخول مطابقة للتصميم المطلوب"""
    
    def __init__(self, parent=None):
        super().__init__("تسجيل الدخول - Flex USA", parent)
        self.user_data = None
        self.setup_professional_login()
        
    def setup_professional_login(self):
        """إعداد شاشة تسجيل الدخول الجديدة"""
        # إعدادات النافذة
        self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)
        
        # إنشاء التخطيط الرئيسي الأفقي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الجانب الأزرق (يمين)
        blue_side = self.create_blue_side()
        main_layout.addWidget(blue_side)
        
        # إنشاء الجانب الأبيض (يسار) 
        white_side = self.create_white_side()
        main_layout.addWidget(white_side)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.connect_events()
        
        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(200, lambda: self.username_input.setFocus())
    
    def create_blue_side(self):
        """إنشاء الجانب الأزرق مع عنوان النظام"""
        blue_widget = QFrame()
        blue_widget.setFixedWidth(450)
        blue_widget.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4A90E2,
                    stop:0.5 #357ABD,
                    stop:1 #2E5F8A);
                border: none;
            }
        """)

        blue_layout = QVBoxLayout()
        blue_layout.setAlignment(Qt.AlignTop | Qt.AlignHCenter)
        blue_layout.setContentsMargins(40, 60, 40, 40)
        blue_layout.setSpacing(20)

        # مساحة علوية صغيرة
        blue_layout.addSpacing(40)

        # عنوان النظام الرئيسي
        main_title = ModernLabel("Flex USA", "title")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-size: 42px;
                font-weight: bold;
                color: white;
                margin-bottom: 15px;
            }
        """)
        blue_layout.addWidget(main_title)

        # العنوان الفرعي
        subtitle = ModernLabel("Accounting System", "subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: rgba(255, 255, 255, 0.9);
                font-weight: 300;
                margin-bottom: 30px;
            }
        """)
        blue_layout.addWidget(subtitle)

        # إضافة معلومات إضافية
        info_label = ModernLabel("نظام المحاسبة المالي الاحترافي", "normal")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 400;
                margin-bottom: 20px;
            }
        """)
        blue_layout.addWidget(info_label)

        # معلومات الإصدار
        version_label = ModernLabel("الإصدار 1.0.0", "small")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.7);
                font-weight: 300;
            }
        """)
        blue_layout.addWidget(version_label)

        # مساحة مرنة لدفع المحتوى للأعلى
        blue_layout.addStretch()

        blue_widget.setLayout(blue_layout)
        return blue_widget
    
    def create_white_side(self):
        """إنشاء الجانب الأبيض مع نموذج تسجيل الدخول"""
        white_widget = QFrame()
        white_widget.setFixedWidth(450)
        white_widget.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
            }
        """)

        white_layout = QVBoxLayout()
        white_layout.setAlignment(Qt.AlignTop | Qt.AlignHCenter)
        white_layout.setContentsMargins(40, 50, 40, 30)
        white_layout.setSpacing(20)

        # مساحة علوية صغيرة
        white_layout.addSpacing(30)

        # عنوان تسجيل الدخول
        login_title = ModernLabel("تسجيل الدخول", "header")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: 8px;
            }}
        """)
        white_layout.addWidget(login_title)

        # النص التوضيحي
        welcome_text = ModernLabel("أهلاً بك، أدخل بياناتك للوصول إلى حسابك.", "normal")
        welcome_text.setAlignment(Qt.AlignCenter)
        welcome_text.setStyleSheet(f"""
            QLabel {{
                font-size: 15px;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: 25px;
                line-height: 1.4;
            }}
        """)
        white_layout.addWidget(welcome_text)

        # نموذج تسجيل الدخول
        form_widget = self.create_simple_form()
        white_layout.addWidget(form_widget)

        # إضافة معلومات تسجيل الدخول التجريبية
        demo_info = self.create_demo_info()
        white_layout.addWidget(demo_info)

        # مساحة مرنة لدفع المحتوى للأعلى
        white_layout.addStretch()

        white_widget.setLayout(white_layout)
        return white_widget
    
    def create_simple_form(self):
        """إنشاء نموذج تسجيل الدخول البسيط"""
        form_widget = QFrame()
        form_widget.setStyleSheet("QFrame { background: transparent; border: none; }")

        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(0, 0, 0, 0)

        # حقل اسم المستخدم
        username_label = ModernLabel("اسم المستخدم", "normal")
        username_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_PRIMARY};
                font-weight: 500;
                margin-bottom: 3px;
            }}
        """)
        form_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("مثال: ahmad.ali")
        self.username_input.setFixedHeight(45)
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 15px;
                background-color: white;
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: #4A90E2;
                outline: none;
            }}
        """)
        form_layout.addWidget(self.username_input)

        # مساحة صغيرة
        form_layout.addSpacing(5)

        # حقل كلمة المرور
        password_label = ModernLabel("كلمة المرور", "normal")
        password_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_PRIMARY};
                font-weight: 500;
                margin-bottom: 3px;
            }}
        """)
        form_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("••••••••••")
        self.password_input.setFixedHeight(45)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 15px;
                background-color: white;
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: #4A90E2;
                outline: none;
            }}
        """)
        form_layout.addWidget(self.password_input)

        # مساحة قبل الأزرار
        form_layout.addSpacing(15)

        # أزرار العمل
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)
        buttons_layout.setContentsMargins(0, 0, 0, 0)

        # زر الإلغاء
        self.cancel_button = ModernButton("إلغاء", "secondary")
        self.cancel_button.setFixedHeight(45)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: 2px solid #E1E8ED;
                border-radius: 8px;
                font-size: 15px;
                font-weight: 500;
                padding: 10px 25px;
            }}
            QPushButton:hover {{
                background-color: #F8F9FA;
                border-color: #D1D9E0;
            }}
        """)
        buttons_layout.addWidget(self.cancel_button)

        # زر تسجيل الدخول
        self.login_button = ModernButton("دخول", "primary")
        self.login_button.setFixedHeight(45)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                font-weight: 600;
                padding: 10px 25px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E5F8A;
            }
        """)
        buttons_layout.addWidget(self.login_button)

        form_layout.addLayout(buttons_layout)

        form_widget.setLayout(form_layout)
        return form_widget

    def create_demo_info(self):
        """إنشاء معلومات تسجيل الدخول التجريبية"""
        demo_widget = QFrame()
        demo_widget.setStyleSheet(f"""
            QFrame {{
                background-color: #F8F9FA;
                border: 1px solid #E1E8ED;
                border-radius: 8px;
                padding: 12px;
                margin-top: 15px;
            }}
        """)

        demo_layout = QVBoxLayout()
        demo_layout.setSpacing(8)
        demo_layout.setContentsMargins(10, 8, 10, 8)

        # عنوان المعلومات التجريبية
        demo_title = ModernLabel("🔑 معلومات تسجيل الدخول التجريبية", "normal")
        demo_title.setAlignment(Qt.AlignCenter)
        demo_title.setStyleSheet(f"""
            QLabel {{
                font-size: 13px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: 5px;
            }}
        """)
        demo_layout.addWidget(demo_title)

        # معلومات الدخول
        credentials_info = ModernLabel("اسم المستخدم: admin | كلمة المرور: admin", "small")
        credentials_info.setAlignment(Qt.AlignCenter)
        credentials_info.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                color: {ModernTheme.TEXT_SECONDARY};
                background-color: white;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #E1E8ED;
            }}
        """)
        demo_layout.addWidget(credentials_info)

        demo_widget.setLayout(demo_layout)
        return demo_widget

    def connect_events(self):
        """ربط الأحداث"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.close)
        
        # ربط Enter للتسجيل
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.handle_login)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # محاكاة عملية التحقق
        if username == "admin" and password == "admin":
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    login_screen = ProfessionalLoginScreen()
    if login_screen.exec_() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    sys.exit()

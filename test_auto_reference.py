#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إنشاء رقم المرجع التلقائي للعمليات المالية
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow
from database import DatabaseManager

def test_auto_reference():
    """اختبار إنشاء رقم المرجع التلقائي"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # اختبار دالة إنشاء رقم المرجع
    print("🎯 اختبار إنشاء رقم المرجع التلقائي")
    
    db = DatabaseManager()
    
    print("\n🔢 أمثلة على أرقام المراجع التلقائية:")
    
    # اختبار أنواع مختلفة من العمليات
    test_cases = [
        ("استلام", "دينار ليبي"),
        ("استلام", "دولار أمريكي"),
        ("تسليم", "دينار ليبي"),
        ("تسليم", "دولار أمريكي"),
    ]
    
    for transaction_type, currency in test_cases:
        ref_number = db.generate_reference_number(transaction_type, currency)
        print(f"   • {transaction_type} - {currency}: {ref_number}")
    
    print("\n📋 تفسير تنسيق رقم المرجع:")
    print("   تنسيق: [نوع العملية]-[العملة]-[التاريخ]-[الرقم التسلسلي]")
    print("   • REC = استلام (Receive)")
    print("   • PAY = تسليم (Payment)")
    print("   • LYD = دينار ليبي (Libyan Dinar)")
    print("   • USD = دولار أمريكي (US Dollar)")
    print("   • التاريخ بتنسيق: YYYYMMDD")
    print("   • الرقم التسلسلي: 001, 002, 003...")
    
    print("\n✨ الميزات الجديدة:")
    print("   • إنشاء تلقائي لرقم المرجع عند فتح نافذة العملية")
    print("   • تحديث تلقائي عند تغيير نوع العملية أو العملة")
    print("   • زر 'إنشاء رقم جديد' لإنشاء رقم مرجع جديد يدوياً")
    print("   • حقل رقم المرجع للقراءة فقط لمنع التعديل العرضي")
    print("   • تنسيق موحد ومنظم لجميع أرقام المراجع")
    print("   • ترقيم تسلسلي يومي لكل نوع عملية")
    
    print("\n🔧 التحسينات التقنية:")
    print("   • دالة generate_reference_number() في قاعدة البيانات")
    print("   • ربط تلقائي مع تغيير نوع العملية والعملة")
    print("   • معالجة الأخطاء مع رقم مرجع احتياطي")
    print("   • واجهة محسنة مع زر إنشاء رقم جديد")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. اضغط على 'عملية جديدة' من شريط الأدوات")
    print("   3. لاحظ رقم المرجع التلقائي في الحقل")
    print("   4. غير نوع العملية أو العملة ولاحظ التحديث التلقائي")
    print("   5. اضغط على 'إنشاء رقم جديد' لإنشاء رقم مختلف")
    print("   6. احفظ العملية ولاحظ رقم المرجع في الجدول")
    
    print("\n💡 نصائح الاستخدام:")
    print("   • رقم المرجع يتم إنشاؤه تلقائياً ولا يحتاج تدخل")
    print("   • كل عملية لها رقم مرجع فريد ومميز")
    print("   • يمكن البحث بسهولة باستخدام رقم المرجع")
    print("   • التنسيق الموحد يسهل التتبع والمراجعة")
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - جرب إضافة عملية جديدة!")
        print("🔥 لاحظ رقم المرجع التلقائي في نافذة العملية الجديدة!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_auto_reference()

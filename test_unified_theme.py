#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام الموحد للألوان والأنماط
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow
from unified_theme import UnifiedTheme

def test_unified_theme():
    """اختبار النظام الموحد للألوان والأنماط"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق الخطوط الموحدة
    UnifiedTheme.apply_fonts(app)
    
    print("🎨 اختبار النظام الموحد للألوان والأنماط")
    
    print("\n✨ النظام الموحد الجديد:")
    
    print("\n🎨 الألوان الأساسية:")
    print(f"   🔵 اللون الرئيسي: {UnifiedTheme.PRIMARY_COLOR} (أزرق-أخضر هادئ)")
    print(f"   🟢 اللون الثانوي: {UnifiedTheme.SECONDARY_COLOR} (تركواز معتدل)")
    print(f"   🟡 الخلفية الفاتحة: {UnifiedTheme.LIGHT_BACKGROUND} (بيج فاتح)")
    print(f"   ⚫ الخلفية الداكنة: {UnifiedTheme.DARK_BACKGROUND} (كحلي داكن)")
    
    print("\n✅ ألوان الحالات:")
    print(f"   ✅ النجاح: {UnifiedTheme.SUCCESS_COLOR} (أخضر)")
    print(f"   ⚠️ التحذير: {UnifiedTheme.WARNING_COLOR} (أصفر برتقالي)")
    print(f"   ❌ الخطأ: {UnifiedTheme.ERROR_COLOR} (أحمر معتدل)")
    print(f"   ℹ️ المعلومات: {UnifiedTheme.INFO_COLOR} (أزرق هادئ)")
    
    print("\n🖋️ أحجام الخطوط:")
    print(f"   📰 عنوان الشاشة: {UnifiedTheme.TITLE_FONT_SIZE}px")
    print(f"   📝 العناوين الفرعية: {UnifiedTheme.SUBTITLE_FONT_SIZE}px")
    print(f"   📄 النصوص العامة: {UnifiedTheme.NORMAL_FONT_SIZE}px")
    print(f"   🔘 الأزرار: {UnifiedTheme.BUTTON_FONT_SIZE}px")
    print(f"   🔤 النصوص الثانوية: {UnifiedTheme.SMALL_FONT_SIZE}px")
    
    print("\n📐 الأبعاد الموحدة:")
    print(f"   🔘 عرض الأزرار: {UnifiedTheme.BUTTON_WIDTH}px")
    print(f"   📏 ارتفاع الأزرار: {UnifiedTheme.BUTTON_HEIGHT}px")
    print(f"   🔄 زاوية الحواف: {UnifiedTheme.BORDER_RADIUS}px")
    print(f"   📦 الهوامش: {UnifiedTheme.PADDING}px")
    print(f"   ↔️ التباعد: {UnifiedTheme.SPACING}px")
    
    print("\n🔘 أنماط الأزرار:")
    print("   🔵 الأزرار الرئيسية:")
    print("      • خلفية متدرجة من التركواز إلى الأزرق")
    print("      • نص أبيض عريض")
    print("      • تأثير hover وتكبير طفيف")
    print("      • تأثير pressed وتصغير طفيف")
    print("   ")
    print("   ⚪ الأزرار الثانوية:")
    print("      • خلفية بيضاء مع حدود ملونة")
    print("      • نص ملون")
    print("      • تحول إلى ملون عند hover")
    print("   ")
    print("   ✅ أزرار النجاح: خلفية خضراء متدرجة")
    print("   ⚠️ أزرار التحذير: خلفية صفراء برتقالية متدرجة")
    print("   ❌ أزرار الخطأ: خلفية حمراء متدرجة")
    
    print("\n📝 حقول الإدخال:")
    print("   • خلفية بيضاء مع حدود رمادية")
    print("   • حدود ملونة عند التركيز")
    print("   • حدود أزرق عند hover")
    print("   • زوايا دائرية وتباعد مناسب")
    print("   • خط واضح ومقروء")
    
    print("\n📊 الجداول:")
    print("   • خلفية بيضاء مع حدود ناعمة")
    print("   • رؤوس أعمدة بخلفية بيج متدرجة")
    print("   • صفوف متناوبة بلون رمادي فاتح")
    print("   • تحديد بلون تركواز شفاف")
    print("   • خطوط شبكة ناعمة")
    
    print("\n🪟 النوافذ الحوارية:")
    print("   • خلفية متدرجة من الأبيض إلى الرمادي الفاتح")
    print("   • عناوين بألوان النظام")
    print("   • نصوص واضحة ومنظمة")
    print("   • زوايا دائرية موحدة")
    
    print("\n🏠 النافذة الرئيسية:")
    print("   • شريط قوائم بلون أزرق داكن")
    print("   • قوائم منسدلة بيضاء مع حدود")
    print("   • شريط حالة بلون أزرق داكن")
    print("   • خلفية رمادية فاتحة")
    
    print("\n🔐 شاشة تسجيل الدخول:")
    print("   • خلفية متدرجة من البيج إلى الأبيض")
    print("   • صندوق تسجيل دخول أبيض مع ظل")
    print("   • حقول إدخال بتصميم موحد")
    print("   • أزرار بألوان النظام")
    
    print("\n🎯 التطبيق التقني:")
    print("   📁 ملف النظام: unified_theme.py")
    print("   🔧 فئة النظام: UnifiedTheme")
    print("   📝 الاستيراد: from unified_theme import UnifiedTheme")
    print("   🎨 التطبيق: UnifiedTheme.get_*_style()")
    
    print("\n🔧 الدوال المتوفرة:")
    print("   • get_primary_button_style(): أزرار رئيسية")
    print("   • get_secondary_button_style(): أزرار ثانوية")
    print("   • get_success_button_style(): أزرار النجاح")
    print("   • get_warning_button_style(): أزرار التحذير")
    print("   • get_error_button_style(): أزرار الخطأ")
    print("   • get_input_field_style(): حقول الإدخال")
    print("   • get_table_style(): الجداول")
    print("   • get_dialog_style(): النوافذ الحوارية")
    print("   • get_main_window_style(): النافذة الرئيسية")
    print("   • get_login_style(): شاشة تسجيل الدخول")
    print("   • apply_fonts(): تطبيق الخطوط")
    
    print("\n✨ الفوائد المحققة:")
    print("   🎨 هوية بصرية موحدة:")
    print("      • ألوان متناسقة عبر النظام")
    print("      • خطوط موحدة وواضحة")
    print("      • أبعاد متسقة للعناصر")
    print("   ")
    print("   🔧 سهولة الصيانة:")
    print("      • تغيير الألوان من مكان واحد")
    print("      • إضافة أنماط جديدة بسهولة")
    print("      • كود منظم ومرتب")
    print("   ")
    print("   📱 تجربة مستخدم محسنة:")
    print("      • مظهر احترافي ومتسق")
    print("      • ألوان مريحة للعين")
    print("      • تفاعل واضح ومفهوم")
    
    print("\n🎨 لوحة الألوان الكاملة:")
    print("   الألوان الأساسية:")
    print(f"   • الرئيسي: {UnifiedTheme.PRIMARY_COLOR}")
    print(f"   • الثانوي: {UnifiedTheme.SECONDARY_COLOR}")
    print(f"   • الخلفية الفاتحة: {UnifiedTheme.LIGHT_BACKGROUND}")
    print(f"   • الخلفية الداكنة: {UnifiedTheme.DARK_BACKGROUND}")
    print("   ")
    print("   ألوان الحالات:")
    print(f"   • النجاح: {UnifiedTheme.SUCCESS_COLOR}")
    print(f"   • التحذير: {UnifiedTheme.WARNING_COLOR}")
    print(f"   • الخطأ: {UnifiedTheme.ERROR_COLOR}")
    print(f"   • المعلومات: {UnifiedTheme.INFO_COLOR}")
    print("   ")
    print("   الألوان المساعدة:")
    print(f"   • الأبيض: {UnifiedTheme.WHITE}")
    print(f"   • الأسود: {UnifiedTheme.BLACK}")
    print(f"   • الرمادي الفاتح: {UnifiedTheme.LIGHT_GRAY}")
    print(f"   • الرمادي المتوسط: {UnifiedTheme.MEDIUM_GRAY}")
    print(f"   • الرمادي الداكن: {UnifiedTheme.DARK_GRAY}")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. لاحظ الألوان الموحدة في النافذة الرئيسية")
    print("   3. افتح النوافذ المختلفة من القوائم")
    print("   4. جرب الأزرار والحقول")
    print("   5. راجع الجداول والقوائم")
    print("   6. لاحظ التناسق في جميع العناصر")
    
    print("\n💡 نصائح للتخصيص:")
    print("   🎨 لتغيير الألوان:")
    print("      1. عدل المتغيرات في UnifiedTheme")
    print("      2. احفظ الملف وأعد تشغيل النظام")
    print("      3. ستتغير الألوان في جميع النوافذ")
    print("   ")
    print("   🖋️ لتغيير الخطوط:")
    print("      1. عدل أحجام الخطوط في UnifiedTheme")
    print("      2. عدل دالة apply_fonts() للخط الافتراضي")
    print("      3. أعد تشغيل النظام")
    
    print("\n⚡ الأداء:")
    print("   • تحميل واحد للأنماط")
    print("   • استخدام مشترك عبر النظام")
    print("   • CSS محسن ومنظم")
    print("   • لا تأثير على الأداء")
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - استمتع بالنظام الموحد!")
        print("🔥 لاحظ الألوان والأنماط الموحدة في جميع العناصر!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_unified_theme()

# دليل الاستخدام - نظام Flex USA المحاسبي

## 🚀 البدء السريع

### 1. تشغيل التطبيق
- **على Windows:** انقر مرتين على ملف `run_flex_usa.bat`
- **أو:** افتح موجه الأوامر واكتب `python main.py`

### 2. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

## 📋 الواجهة الرئيسية

### لوحة التحكم
تعرض لوحة التحكم الرئيسية:
- **الدينار الليبي:** المبالغ المستلمة والمسلمة والرصيد
- **الدولار الأمريكي:** المبالغ المستلمة والمسلمة والرصيد
- **جدول العمليات:** قائمة بجميع العمليات المالية

---

## 💰 إضافة عملية مالية جديدة

### الخطوات:
1. اضغط على زر **"عملية جديدة"** من شريط الأدوات
2. املأ البيانات التالية:
   - **نوع العملية:** استلام أو تسليم
   - **العملة:** دينار ليبي أو دولار أمريكي
   - **المبلغ:** قيمة العملية
   - **سعر الصرف:** (يتم تحديثه تلقائياً للدولار)
   - **التاريخ:** تاريخ العملية
   - **اسم الجهة:** اسم الزبون أو الموقع أو الوسيط
   - **رقم المرجع:** رقم الفاتورة أو المرجع
   - **الوصف:** وصف إضافي للعملية
3. اضغط **"حفظ"**

### مثال عملي:
- **نوع العملية:** استلام
- **العملة:** دولار أمريكي
- **المبلغ:** 100.00
- **اسم الجهة:** زبون أحمد
- **المرجع:** INV-001
- **الوصف:** دفعة مقدمة لطلبية من أمازون

---

## 📊 إنشاء التقارير

### التقرير اليومي:
1. من قائمة **"التقارير"** اختر **"تقرير يومي"**
2. اختر نوع التصدير: **PDF** أو **Excel**
3. سيتم حفظ التقرير في مجلد `reports`

### التقرير الشهري:
1. من قائمة **"التقارير"** اختر **"تقرير شهري"**
2. اختر نوع التصدير: **PDF** أو **Excel**
3. سيتم حفظ التقرير في مجلد `reports`

---

## 💱 إدارة أسعار الصرف

### إضافة سعر صرف جديد:
1. من قائمة **"إعدادات"** اختر **"أسعار الصرف"**
2. املأ البيانات:
   - **من العملة:** دولار أمريكي
   - **إلى العملة:** دينار ليبي
   - **السعر:** مثال 4.8000
   - **التاريخ:** تاريخ السعر
3. اضغط **"إضافة السعر"**

---

## 💾 النسخ الاحتياطية

### إنشاء نسخة احتياطية:
1. من قائمة **"ملف"** اختر **"نسخة احتياطية"**
2. اضغط **"إنشاء نسخة احتياطية"**
3. سيتم حفظ النسخة في مجلد `backups`

### استرجاع نسخة احتياطية:
1. من نافذة النسخ الاحتياطية
2. اختر النسخة المطلوبة
3. اضغط **"استرجاع"**
4. أكد العملية

---

## 🔧 نصائح مهمة

### الأمان:
- غيّر كلمة مرور المدير الافتراضية
- قم بعمل نسخة احتياطية يومياً
- احتفظ بنسخة من قاعدة البيانات في مكان آمن

### الاستخدام الأمثل:
- أدخل العمليات فور حدوثها
- تأكد من صحة أسعار الصرف
- راجع التقارير بانتظام
- احتفظ بالمراجع والفواتير

### حل المشاكل الشائعة:

#### التطبيق لا يفتح:
- تأكد من تثبيت Python
- تأكد من تثبيت PyQt5
- شغل `pip install -r requirements.txt`

#### خطأ في قاعدة البيانات:
- استرجع آخر نسخة احتياطية
- أو احذف ملف `flex_usa.db` ليتم إنشاؤه من جديد

#### مشاكل في التقارير:
- تأكد من وجود مجلد `reports`
- تأكد من صلاحيات الكتابة

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من ملف `README.md`
2. راجع رسائل الخطأ في موجه الأوامر
3. تأكد من تثبيت جميع المتطلبات
4. جرب إعادة تشغيل التطبيق

### معلومات النظام:
- **الإصدار:** 1.0.0
- **قاعدة البيانات:** SQLite
- **واجهة المستخدم:** PyQt5
- **التقارير:** PDF و Excel

---

## 🎯 أمثلة عملية

### سيناريو 1: شراء من موقع إلكتروني
1. **استلام** 200 دولار من الزبون
2. **تسليم** 180 دولار لموقع أمازون
3. الربح: 20 دولار

### سيناريو 2: تحويل عملة
1. **استلام** 100 دولار من الزبون
2. **تسليم** 480 دينار للزبون (بسعر 4.8)
3. العملية: تحويل عملة

### سيناريو 3: عمولة وسيط
1. **استلام** 1000 دينار من الزبون
2. **تسليم** 200 دولار للموقع
3. **تسليم** 40 دينار عمولة للوسيط
4. الباقي: ربح العملية

---

**نتمنى لك تجربة ممتعة مع نظام Flex USA المحاسبي! 🎉**

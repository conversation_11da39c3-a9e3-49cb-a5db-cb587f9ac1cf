#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة تسجيل الدخول الاحترافية
"""

import sys
from PyQt5.QtWidgets import QApplication
from login_dialog import ProfessionalLoginDialog

def test_login_dialog():
    """اختبار شاشة تسجيل الدخول"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(2)  # RTL للعربية
    
    # إنشاء وعرض نافذة تسجيل الدخول
    login_dialog = ProfessionalLoginDialog()
    
    if login_dialog.exec_() == login_dialog.Accepted:
        print(f"تم تسجيل الدخول بنجاح!")
        print(f"اسم المستخدم: {login_dialog.username_input.text()}")
        print(f"كلمة المرور: {'*' * len(login_dialog.password_input.text())}")
        print(f"تذكر كلمة المرور: {login_dialog.remember_checkbox.isChecked()}")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    sys.exit(0)

if __name__ == "__main__":
    test_login_dialog()

@echo off
echo ========================================
echo    Flex USA - نظام المحاسبة المالي
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo تم العثور على Python...

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متاح
    pause
    exit /b 1
)

echo تم العثور على pip...

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo جاري التحقق من المتطلبات...
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo تم تثبيت جميع المتطلبات بنجاح...
echo.
echo جاري تشغيل نظام Flex USA...
echo.

REM تشغيل التطبيق
python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    pause
)

echo.
echo تم إغلاق التطبيق
pause

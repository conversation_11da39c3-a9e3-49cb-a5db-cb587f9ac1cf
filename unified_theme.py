#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الألوان والأنماط الموحد للنظام
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class UnifiedTheme:
    """نظام الألوان والأنماط الموحد"""
    
    # 🎨 الألوان الأساسية (Primary Colors)
    PRIMARY_COLOR = "#005F73"        # أزرق-أخضر هادئ
    SECONDARY_COLOR = "#0A9396"      # تركواز معتدل
    LIGHT_BACKGROUND = "#E9D8A6"     # بيج فاتح
    DARK_BACKGROUND = "#001219"      # كحلي داكن جداً
    
    # ✅ ألوان الحالات
    SUCCESS_COLOR = "#52B788"        # أخضر النجاح
    WARNING_COLOR = "#E9C46A"        # أصفر برتقالي التحذير
    ERROR_COLOR = "#E76F51"          # أحمر معتدل الخطأ
    INFO_COLOR = "#3A86FF"           # أزرق هادئ المعلومات
    
    # 🎨 ألوان إضافية
    WHITE = "#FFFFFF"
    BLACK = "#000000"
    LIGHT_GRAY = "#F6F6F6"
    MEDIUM_GRAY = "#CCCCCC"
    DARK_GRAY = "#666666"
    
    # 🖋️ أحجام الخطوط
    TITLE_FONT_SIZE = 24             # عنوان الشاشة
    SUBTITLE_FONT_SIZE = 18          # العناوين الفرعية
    NORMAL_FONT_SIZE = 16            # النصوص العامة
    BUTTON_FONT_SIZE = 16            # أزرار
    SMALL_FONT_SIZE = 12             # نصوص ثانوية
    
    # 📐 الأبعاد
    BUTTON_WIDTH = 180               # عرض الأزرار
    BUTTON_HEIGHT = 45               # ارتفاع الأزرار
    BORDER_RADIUS = 8                # زاوية الحواف
    PADDING = 15                     # الهوامش
    SPACING = 10                     # التباعد
    
    @staticmethod
    def get_primary_button_style():
        """نمط الأزرار الرئيسية"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.SECONDARY_COLOR}, 
                    stop:1 {UnifiedTheme.PRIMARY_COLOR});
                color: {UnifiedTheme.WHITE};
                border: none;
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.BUTTON_FONT_SIZE}px;
                font-weight: bold;
                min-width: {UnifiedTheme.BUTTON_WIDTH}px;
                min-height: {UnifiedTheme.BUTTON_HEIGHT}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #38B2AC, 
                    stop:1 {UnifiedTheme.SECONDARY_COLOR});
                transform: scale(1.02);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.PRIMARY_COLOR}, 
                    stop:1 {UnifiedTheme.DARK_BACKGROUND});
                transform: scale(0.98);
            }}
        """
    
    @staticmethod
    def get_secondary_button_style():
        """نمط الأزرار الثانوية"""
        return f"""
            QPushButton {{
                background: {UnifiedTheme.WHITE};
                color: {UnifiedTheme.SECONDARY_COLOR};
                border: 2px solid {UnifiedTheme.SECONDARY_COLOR};
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.BUTTON_FONT_SIZE}px;
                font-weight: bold;
                min-width: {UnifiedTheme.BUTTON_WIDTH}px;
                min-height: {UnifiedTheme.BUTTON_HEIGHT}px;
            }}
            QPushButton:hover {{
                background: {UnifiedTheme.SECONDARY_COLOR};
                color: {UnifiedTheme.WHITE};
            }}
            QPushButton:pressed {{
                background: {UnifiedTheme.PRIMARY_COLOR};
                color: {UnifiedTheme.WHITE};
            }}
        """
    
    @staticmethod
    def get_success_button_style():
        """نمط أزرار النجاح"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.SUCCESS_COLOR}, 
                    stop:1 #2D6A4F);
                color: {UnifiedTheme.WHITE};
                border: none;
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.BUTTON_FONT_SIZE}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #74C69D, 
                    stop:1 {UnifiedTheme.SUCCESS_COLOR});
            }}
        """
    
    @staticmethod
    def get_warning_button_style():
        """نمط أزرار التحذير"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.WARNING_COLOR}, 
                    stop:1 #D4A574);
                color: {UnifiedTheme.WHITE};
                border: none;
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.BUTTON_FONT_SIZE}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F1D18A, 
                    stop:1 {UnifiedTheme.WARNING_COLOR});
            }}
        """
    
    @staticmethod
    def get_error_button_style():
        """نمط أزرار الخطأ"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.ERROR_COLOR}, 
                    stop:1 #C44536);
                color: {UnifiedTheme.WHITE};
                border: none;
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.BUTTON_FONT_SIZE}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #EE8B75, 
                    stop:1 {UnifiedTheme.ERROR_COLOR});
            }}
        """
    
    @staticmethod
    def get_input_field_style():
        """نمط حقول الإدخال"""
        return f"""
            QLineEdit, QTextEdit, QComboBox {{
                background: {UnifiedTheme.WHITE};
                border: 2px solid {UnifiedTheme.MEDIUM_GRAY};
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                padding: {UnifiedTheme.PADDING}px;
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
                color: {UnifiedTheme.BLACK};
            }}
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border: 2px solid {UnifiedTheme.SECONDARY_COLOR};
                background: {UnifiedTheme.WHITE};
            }}
            QLineEdit:hover, QTextEdit:hover, QComboBox:hover {{
                border: 2px solid {UnifiedTheme.PRIMARY_COLOR};
            }}
        """
    
    @staticmethod
    def get_table_style():
        """نمط الجداول"""
        return f"""
            QTableWidget {{
                background: {UnifiedTheme.WHITE};
                border: 2px solid {UnifiedTheme.MEDIUM_GRAY};
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                gridline-color: {UnifiedTheme.LIGHT_GRAY};
                selection-background-color: rgba(10, 147, 150, 0.3);
                alternate-background-color: {UnifiedTheme.LIGHT_GRAY};
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
            }}
            QTableWidget::item {{
                padding: {UnifiedTheme.PADDING}px;
                border: none;
                border-bottom: 1px solid {UnifiedTheme.LIGHT_GRAY};
            }}
            QTableWidget::item:selected {{
                background: rgba(10, 147, 150, 0.2);
                color: {UnifiedTheme.BLACK};
                border: 1px solid {UnifiedTheme.SECONDARY_COLOR};
            }}
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.LIGHT_BACKGROUND}, 
                    stop:1 #D4C5A0);
                color: {UnifiedTheme.BLACK};
                padding: {UnifiedTheme.PADDING}px;
                border: none;
                font-weight: bold;
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
                border-right: 1px solid {UnifiedTheme.MEDIUM_GRAY};
            }}
        """
    
    @staticmethod
    def get_dialog_style():
        """نمط النوافذ الحوارية"""
        return f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.WHITE}, 
                    stop:1 {UnifiedTheme.LIGHT_GRAY});
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
            }}
            QLabel {{
                color: {UnifiedTheme.BLACK};
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
                font-weight: normal;
            }}
            QLabel[class="title"] {{
                font-size: {UnifiedTheme.TITLE_FONT_SIZE}px;
                font-weight: bold;
                color: {UnifiedTheme.PRIMARY_COLOR};
            }}
            QLabel[class="subtitle"] {{
                font-size: {UnifiedTheme.SUBTITLE_FONT_SIZE}px;
                font-weight: bold;
                color: {UnifiedTheme.SECONDARY_COLOR};
            }}
        """
    
    @staticmethod
    def get_main_window_style():
        """نمط النافذة الرئيسية"""
        return f"""
            QMainWindow {{
                background: {UnifiedTheme.LIGHT_GRAY};
            }}
            QMenuBar {{
                background: {UnifiedTheme.PRIMARY_COLOR};
                color: {UnifiedTheme.WHITE};
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
                font-weight: bold;
                padding: 5px;
            }}
            QMenuBar::item {{
                background: transparent;
                padding: 8px 15px;
                border-radius: 4px;
            }}
            QMenuBar::item:selected {{
                background: {UnifiedTheme.SECONDARY_COLOR};
            }}
            QMenu {{
                background: {UnifiedTheme.WHITE};
                border: 2px solid {UnifiedTheme.MEDIUM_GRAY};
                border-radius: {UnifiedTheme.BORDER_RADIUS}px;
                color: {UnifiedTheme.BLACK};
                font-size: {UnifiedTheme.NORMAL_FONT_SIZE}px;
            }}
            QMenu::item {{
                padding: 8px 20px;
                border-radius: 4px;
            }}
            QMenu::item:selected {{
                background: {UnifiedTheme.SECONDARY_COLOR};
                color: {UnifiedTheme.WHITE};
            }}
            QStatusBar {{
                background: {UnifiedTheme.PRIMARY_COLOR};
                color: {UnifiedTheme.WHITE};
                font-size: {UnifiedTheme.SMALL_FONT_SIZE}px;
            }}
        """
    
    @staticmethod
    def get_login_style():
        """نمط شاشة تسجيل الدخول"""
        return f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {UnifiedTheme.LIGHT_BACKGROUND}, 
                    stop:1 {UnifiedTheme.WHITE});
            }}
            QFrame[class="login_box"] {{
                background: {UnifiedTheme.WHITE};
                border: none;
                border-radius: {UnifiedTheme.BORDER_RADIUS * 2}px;
                padding: {UnifiedTheme.PADDING * 2}px;
            }}
        """
    
    @staticmethod
    def apply_fonts(app):
        """تطبيق الخطوط على التطبيق"""
        # الخط الرئيسي
        font = QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(UnifiedTheme.NORMAL_FONT_SIZE)
        app.setFont(font)
        
        return font

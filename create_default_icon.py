#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء أيقونة افتراضية للنظام
"""

import os
from PyQt5.QtGui import QPixmap, QPainter, QFont, QColor, QBrush, QPen
from PyQt5.QtCore import Qt

def create_system_icon():
    """إنشاء أيقونة النظام الافتراضية"""
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    if not os.path.exists("assets"):
        os.makedirs("assets")
    
    # إنشاء pixmap بحجم 128x128 للجودة العالية
    pixmap = QPixmap(128, 128)
    pixmap.fill(Qt.transparent)  # خلفية شفافة
    
    painter = Q<PERSON>ainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    painter.setRenderHint(QPainter.TextAntialiasing)
    
    # رسم الخلفية الدائرية
    painter.setBrush(QBrush(QColor(102, 126, 234)))  # أزرق النظام
    painter.setPen(QPen(QColor(118, 75, 162), 4))    # حدود بنفسجية
    painter.drawEllipse(8, 8, 112, 112)
    
    # رسم دائرة داخلية للعمق
    painter.setBrush(QBrush(QColor(118, 75, 162, 100)))  # بنفسجي شفاف
    painter.setPen(Qt.NoPen)
    painter.drawEllipse(20, 20, 88, 88)
    
    # رسم النص الرئيسي
    painter.setPen(QPen(QColor(255, 255, 255), 2))  # نص أبيض
    font = QFont("Arial", 48, QFont.Bold)
    painter.setFont(font)
    painter.drawText(pixmap.rect(), Qt.AlignCenter, "F")
    
    # رسم نص فرعي
    painter.setPen(QPen(QColor(255, 255, 255, 180), 1))  # نص أبيض شفاف
    font_small = QFont("Arial", 16, QFont.Bold)
    painter.setFont(font_small)
    
    # حساب موقع النص الفرعي
    text_rect = pixmap.rect()
    text_rect.setTop(text_rect.center().y() + 25)
    painter.drawText(text_rect, Qt.AlignCenter, "USA")
    
    painter.end()
    
    # حفظ الأيقونة
    icon_path = "assets/icon.png"
    if pixmap.save(icon_path, "PNG"):
        print(f"✅ تم إنشاء الأيقونة بنجاح: {icon_path}")
        return True
    else:
        print("❌ فشل في حفظ الأيقونة")
        return False

def create_alternative_icons():
    """إنشاء أيقونات بديلة بتصاميم مختلفة"""
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    if not os.path.exists("assets"):
        os.makedirs("assets")
    
    # أيقونة مربعة
    pixmap_square = QPixmap(128, 128)
    pixmap_square.fill(Qt.transparent)
    
    painter = QPainter(pixmap_square)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # رسم مربع مع زوايا دائرية
    painter.setBrush(QBrush(QColor(102, 126, 234)))
    painter.setPen(QPen(QColor(118, 75, 162), 4))
    painter.drawRoundedRect(8, 8, 112, 112, 20, 20)
    
    # النص
    painter.setPen(QPen(QColor(255, 255, 255), 2))
    font = QFont("Arial", 48, QFont.Bold)
    painter.setFont(font)
    painter.drawText(pixmap_square.rect(), Qt.AlignCenter, "F")
    
    painter.end()
    
    # حفظ الأيقونة المربعة
    square_path = "assets/icon_square.png"
    if pixmap_square.save(square_path, "PNG"):
        print(f"✅ تم إنشاء الأيقونة المربعة: {square_path}")
    
    # أيقونة بتدرج
    pixmap_gradient = QPixmap(128, 128)
    pixmap_gradient.fill(Qt.transparent)
    
    painter = QPainter(pixmap_gradient)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # تدرج لوني
    from PyQt5.QtGui import QLinearGradient
    gradient = QLinearGradient(0, 0, 128, 128)
    gradient.setColorAt(0, QColor(102, 126, 234))
    gradient.setColorAt(1, QColor(118, 75, 162))
    
    painter.setBrush(QBrush(gradient))
    painter.setPen(QPen(QColor(255, 255, 255), 3))
    painter.drawEllipse(8, 8, 112, 112)
    
    # النص مع ظل
    painter.setPen(QPen(QColor(0, 0, 0, 100), 3))  # ظل
    font = QFont("Arial", 48, QFont.Bold)
    painter.setFont(font)
    painter.drawText(pixmap_gradient.rect().adjusted(2, 2, 2, 2), Qt.AlignCenter, "F")
    
    painter.setPen(QPen(QColor(255, 255, 255), 2))  # النص الأساسي
    painter.drawText(pixmap_gradient.rect(), Qt.AlignCenter, "F")
    
    painter.end()
    
    # حفظ الأيقونة المتدرجة
    gradient_path = "assets/icon_gradient.png"
    if pixmap_gradient.save(gradient_path, "PNG"):
        print(f"✅ تم إنشاء الأيقونة المتدرجة: {gradient_path}")

def main():
    """الدالة الرئيسية"""
    print("🎨 إنشاء أيقونات النظام")
    
    # إنشاء تطبيق مؤقت للرسم
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    print("\n🔧 إنشاء الأيقونة الرئيسية...")
    if create_system_icon():
        print("✅ تم إنشاء الأيقونة الرئيسية بنجاح")
    
    print("\n🎨 إنشاء الأيقونات البديلة...")
    create_alternative_icons()
    
    print("\n📁 الأيقونات المتوفرة:")
    assets_dir = "assets"
    if os.path.exists(assets_dir):
        for file in os.listdir(assets_dir):
            if file.endswith('.png'):
                file_path = os.path.join(assets_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   🖼️ {file} ({file_size} بايت)")
    
    print("\n💡 نصائح الاستخدام:")
    print("   • استخدم icon.png كأيقونة رئيسية")
    print("   • يمكنك تعديل الألوان في الكود")
    print("   • الأيقونات بحجم 128x128 للجودة العالية")
    print("   • تدعم الشفافية (PNG)")
    
    print("\n🚀 لاستخدام الأيقونة:")
    print("   1. شغل النظام: python main.py")
    print("   2. ستظهر الأيقونة تلقائياً في جميع النوافذ")
    print("   3. يمكنك استبدال icon.png بأيقونة مخصصة")
    
    app.quit()

if __name__ == "__main__":
    main()

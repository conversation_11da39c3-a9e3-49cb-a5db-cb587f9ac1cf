#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار دعم اللغة العربية من اليمين إلى اليسار (RTL)
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_rtl_support():
    """اختبار دعم RTL في نافذة بسيطة"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء نافذة اختبار
    window = QWidget()
    window.setWindowTitle("اختبار دعم اللغة العربية - RTL")
    window.setGeometry(300, 300, 500, 400)
    window.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق أنماط RTL
    window.setStyleSheet("""
        QWidget {
            background: #f8f9fa;
            font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
            direction: rtl;
        }
        QLabel {
            text-align: right;
            qproperty-alignment: AlignRight;
            padding: 10px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin: 5px;
        }
        QLineEdit {
            text-align: right;
            qproperty-alignment: AlignRight;
            padding: 10px;
            border: 2px solid #667eea;
            border-radius: 5px;
            margin: 5px;
        }
        QTextEdit {
            text-align: right;
            qproperty-alignment: AlignRight;
            padding: 10px;
            border: 2px solid #667eea;
            border-radius: 5px;
            margin: 5px;
        }
        QPushButton {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            margin: 5px;
        }
        QPushButton:hover {
            background: #764ba2;
        }
    """)
    
    # إنشاء التخطيط
    layout = QVBoxLayout()
    
    # عنوان
    title = QLabel("🎯 اختبار دعم اللغة العربية من اليمين إلى اليسار")
    title.setStyleSheet("font-size: 16px; font-weight: bold; color: #667eea;")
    layout.addWidget(title)
    
    # نص تجريبي
    sample_text = QLabel("هذا نص تجريبي باللغة العربية لاختبار الاتجاه من اليمين إلى اليسار")
    layout.addWidget(sample_text)
    
    # حقل إدخال
    input_field = QLineEdit()
    input_field.setPlaceholderText("اكتب هنا باللغة العربية...")
    layout.addWidget(input_field)
    
    # منطقة نص كبيرة
    text_area = QTextEdit()
    text_area.setPlaceholderText("منطقة نص كبيرة لاختبار الكتابة باللغة العربية من اليمين إلى اليسار...")
    text_area.setMaximumHeight(100)
    layout.addWidget(text_area)
    
    # قائمة منسدلة
    combo_layout = QHBoxLayout()
    combo_label = QLabel("اختر عملة:")
    combo_box = QComboBox()
    combo_box.addItems(["دينار ليبي", "دولار أمريكي", "يورو", "جنيه إسترليني"])
    combo_layout.addWidget(combo_label)
    combo_layout.addWidget(combo_box)
    layout.addLayout(combo_layout)
    
    # أزرار
    button_layout = QHBoxLayout()
    
    test_button = QPushButton("اختبار")
    test_button.clicked.connect(lambda: show_test_result(input_field.text(), text_area.toPlainText()))
    
    clear_button = QPushButton("مسح")
    clear_button.clicked.connect(lambda: clear_fields(input_field, text_area))
    
    close_button = QPushButton("إغلاق")
    close_button.clicked.connect(window.close)
    
    button_layout.addWidget(test_button)
    button_layout.addWidget(clear_button)
    button_layout.addWidget(close_button)
    layout.addLayout(button_layout)
    
    # معلومات إضافية
    info_label = QLabel("""
    📋 معلومات الاختبار:
    • النص يجب أن يظهر من اليمين إلى اليسار
    • حقول الإدخال تبدأ من اليمين
    • الأزرار مرتبة من اليمين إلى اليسار
    • القوائم المنسدلة تفتح من اليمين
    """)
    info_label.setStyleSheet("background: #e8ecf7; color: #2c3e50; font-size: 11px;")
    layout.addWidget(info_label)
    
    window.setLayout(layout)
    
    def show_test_result(input_text, text_area_content):
        """عرض نتيجة الاختبار"""
        msg = QMessageBox(window)
        msg.setWindowTitle("نتيجة الاختبار")
        msg.setText("تم اختبار النصوص بنجاح!")
        msg.setInformativeText(f"النص المدخل: {input_text}\nمحتوى المنطقة: {text_area_content}")
        msg.setLayoutDirection(Qt.RightToLeft)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                color: #2c3e50;
            }
            QMessageBox QPushButton {
                background: #667eea;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: #764ba2;
            }
        """)
        msg.exec_()
    
    def clear_fields(input_field, text_area):
        """مسح الحقول"""
        input_field.clear()
        text_area.clear()
    
    # عرض النافذة
    window.show()
    
    print("🎯 تم تشغيل اختبار دعم RTL")
    print("📝 جرب كتابة نصوص عربية في الحقول")
    print("🔍 تحقق من اتجاه النص والتخطيط")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_rtl_support()

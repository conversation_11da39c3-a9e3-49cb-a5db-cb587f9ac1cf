#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار القوائم الجديدة في الشريط العلوي
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_new_menus():
    """اختبار القوائم الجديدة"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    print("🎯 تم إضافة القوائم الجديدة في الشريط العلوي")
    print("\n📋 ترتيب القوائم الجديد:")
    print("   1. ملف")
    print("   2. التقارير") 
    print("   3. إعدادات")
    print("   4. المساعدة ← جديد")
    print("   5. الخروج ← جديد")
    
    print("\n🔧 محتويات قائمة المساعدة:")
    print("   • 📖 دليل المستخدم")
    print("   • ⌨️ اختصارات لوحة المفاتيح")
    print("   • ℹ️ حول البرنامج")
    
    print("\n🚪 محتويات قائمة الخروج:")
    print("   • 🔓 تسجيل خروج")
    print("   • ❌ إغلاق البرنامج")
    
    print("\n✨ الميزات الجديدة:")
    print("   • دليل مستخدم شامل مع تعليمات مفصلة")
    print("   • جدول اختصارات لوحة المفاتيح")
    print("   • نافذة معلومات البرنامج الاحترافية")
    print("   • خيار تسجيل خروج منفصل عن إغلاق البرنامج")
    print("   • تصميم عربي RTL لجميع النوافذ")
    
    print("\n🔑 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. جرب قائمة 'المساعدة' واستكشف محتوياتها")
    print("   3. جرب قائمة 'الخروج' لرؤية الخيارات المتاحة")
    print("   4. اختبر اختصارات لوحة المفاتيح")
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - يمكنك الآن اختبار القوائم الجديدة!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_new_menus()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد بيانات تجريبية لنظام Flex USA المحاسبي
"""

import sqlite3
from datetime import datetime, date, timedelta
import random

def setup_demo_data():
    """إنشاء بيانات تجريبية للنظام"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('flex_usa.db')
    cursor = conn.cursor()
    
    print("🔄 جاري إعداد البيانات التجريبية...")
    
    # إضافة مستخدمين إضافيين
    users_data = [
        ('cashier1', 'cash123', 'cashier'),
        ('data_entry1', 'data123', 'data_entry'),
        ('manager1', 'mgr123', 'admin')
    ]
    
    for username, password, role in users_data:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, password, role) 
                VALUES (?, ?, ?)
            ''', (username, password, role))
        except:
            pass
    
    print("✅ تم إضافة المستخدمين التجريبيين")
    
    # إضافة أسعار صرف تجريبية
    exchange_rates_data = [
        ('دولار أمريكي', 'دينار ليبي', 4.8000, '2024-01-01'),
        ('دولار أمريكي', 'دينار ليبي', 4.8500, '2024-01-15'),
        ('دولار أمريكي', 'دينار ليبي', 4.7800, '2024-02-01'),
        ('دولار أمريكي', 'دينار ليبي', 4.8200, '2024-02-15'),
        ('دينار ليبي', 'دولار أمريكي', 0.2083, '2024-01-01'),
    ]
    
    for from_curr, to_curr, rate, rate_date in exchange_rates_data:
        cursor.execute('''
            INSERT OR IGNORE INTO exchange_rates (from_currency, to_currency, rate, date)
            VALUES (?, ?, ?, ?)
        ''', (from_curr, to_curr, rate, rate_date))
    
    print("✅ تم إضافة أسعار الصرف التجريبية")
    
    # إضافة عمليات مالية تجريبية
    print("🔄 جاري إضافة العمليات المالية التجريبية...")
    
    # أسماء جهات تجريبية
    parties = [
        'زبون أحمد محمد',
        'زبون فاطمة علي', 
        'موقع أمازون',
        'موقع علي إكسبريس',
        'وسيط التحويلات',
        'زبون خالد سالم',
        'زبون مريم حسن',
        'موقع إيباي',
        'شركة الشحن',
        'زبون عمر يوسف'
    ]
    
    # أوصاف تجريبية
    descriptions = [
        'دفعة مقدمة لطلبية إلكترونيات',
        'تحويل عملة للزبون',
        'شراء ملابس من الموقع',
        'عمولة وسيط',
        'دفع رسوم شحن',
        'استلام دفعة من زبون',
        'شراء أجهزة منزلية',
        'تسليم مبلغ للموقع',
        'عملية صرف عملة',
        'دفعة نهائية للطلبية'
    ]
    
    # إنشاء عمليات للشهرين الماضيين
    start_date = date.today() - timedelta(days=60)
    
    for i in range(100):  # 100 عملية تجريبية
        # تاريخ عشوائي في آخر 60 يوم
        random_days = random.randint(0, 60)
        transaction_date = start_date + timedelta(days=random_days)
        
        # نوع العملية عشوائي
        transaction_type = random.choice(['استلام', 'تسليم'])
        
        # عملة عشوائية
        currency = random.choice(['دينار ليبي', 'دولار أمريكي'])
        
        # مبلغ عشوائي
        if currency == 'دولار أمريكي':
            amount = round(random.uniform(50, 500), 2)
            exchange_rate = round(random.uniform(4.7, 4.9), 4)
        else:
            amount = round(random.uniform(200, 2000), 2)
            exchange_rate = 1.0000
        
        # جهة عشوائية
        party_name = random.choice(parties)
        
        # رقم مرجع
        reference_number = f"REF-{i+1:04d}"
        
        # وصف عشوائي
        description = random.choice(descriptions)
        
        # مستخدم عشوائي (1 = admin)
        user_id = 1
        
        cursor.execute('''
            INSERT INTO transactions 
            (transaction_type, currency, amount, exchange_rate, date, party_name, 
             reference_number, description, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (transaction_type, currency, amount, exchange_rate, 
              transaction_date.strftime('%Y-%m-%d'), party_name, 
              reference_number, description, user_id))
    
    print("✅ تم إضافة 100 عملية مالية تجريبية")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("🎉 تم إعداد البيانات التجريبية بنجاح!")
    print("\n📋 معلومات الحسابات التجريبية:")
    print("👤 المدير: admin / admin123")
    print("👤 الكاشير: cashier1 / cash123") 
    print("👤 مدخل البيانات: data_entry1 / data123")
    print("👤 مدير إضافي: manager1 / mgr123")
    
    # عرض إحصائيات سريعة
    conn = sqlite3.connect('flex_usa.db')
    cursor = conn.cursor()
    
    # إحصائيات العمليات
    cursor.execute("SELECT COUNT(*) FROM transactions")
    total_transactions = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM transactions WHERE currency = 'دولار أمريكي'")
    usd_transactions = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM transactions WHERE currency = 'دينار ليبي'")
    lyd_transactions = cursor.fetchone()[0]
    
    # إحصائيات المبالغ
    cursor.execute('''
        SELECT 
            SUM(CASE WHEN transaction_type = 'استلام' AND currency = 'دولار أمريكي' THEN amount ELSE 0 END) as usd_received,
            SUM(CASE WHEN transaction_type = 'تسليم' AND currency = 'دولار أمريكي' THEN amount ELSE 0 END) as usd_paid,
            SUM(CASE WHEN transaction_type = 'استلام' AND currency = 'دينار ليبي' THEN amount ELSE 0 END) as lyd_received,
            SUM(CASE WHEN transaction_type = 'تسليم' AND currency = 'دينار ليبي' THEN amount ELSE 0 END) as lyd_paid
        FROM transactions
    ''')
    
    result = cursor.fetchone()
    usd_received, usd_paid, lyd_received, lyd_paid = result
    
    conn.close()
    
    print(f"\n📊 إحصائيات البيانات التجريبية:")
    print(f"📈 إجمالي العمليات: {total_transactions}")
    print(f"💵 عمليات الدولار: {usd_transactions}")
    print(f"🏛️ عمليات الدينار: {lyd_transactions}")
    print(f"\n💰 الأرصدة:")
    print(f"💵 الدولار - مستلم: ${usd_received:.2f} | مسلم: ${usd_paid:.2f} | الرصيد: ${(usd_received or 0) - (usd_paid or 0):.2f}")
    print(f"🏛️ الدينار - مستلم: {lyd_received:.2f} د.ل | مسلم: {lyd_paid:.2f} د.ل | الرصيد: {(lyd_received or 0) - (lyd_paid or 0):.2f} د.ل")

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    import os
    
    if os.path.exists('flex_usa.db'):
        os.remove('flex_usa.db')
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    # إعادة إنشاء قاعدة البيانات
    from database import DatabaseManager
    db = DatabaseManager()
    print("🆕 تم إنشاء قاعدة بيانات جديدة")

if __name__ == "__main__":
    print("🚀 مرحباً بك في إعداد البيانات التجريبية لنظام Flex USA")
    print("=" * 60)
    
    choice = input("هل تريد إعادة تعيين قاعدة البيانات؟ (y/n): ").lower()
    
    if choice == 'y':
        reset_database()
    
    setup_demo_data()
    
    print("\n" + "=" * 60)
    print("✨ يمكنك الآن تشغيل التطبيق باستخدام: python main.py")
    print("🔑 استخدم admin / admin123 لتسجيل الدخول")
    
    input("\nاضغط Enter للمتابعة...")

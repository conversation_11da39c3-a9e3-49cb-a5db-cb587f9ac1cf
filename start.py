#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل مبسط لنظام Flex USA المحاسبي
"""

import os
import sys
import subprocess

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 جاري التحقق من المتطلبات...")
    
    # التحقق من Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except:
        print("❌ خطأ: Python غير مثبت")
        return False
    
    # التحقق من PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 متاح")
    except ImportError:
        print("❌ PyQt5 غير مثبت")
        print("🔧 جاري تثبيت PyQt5...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt5"])
            print("✅ تم تثبيت PyQt5")
        except:
            print("❌ فشل في تثبيت PyQt5")
            return False
    
    # التحقق من المكتبات الأخرى
    required_packages = [
        'reportlab',
        'openpyxl', 
        'requests',
        'python-dateutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير مثبت")
    
    # تثبيت المكتبات المفقودة
    if missing_packages:
        print("🔧 جاري تثبيت المكتبات المفقودة...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
            print("✅ تم تثبيت جميع المكتبات")
        except:
            print("❌ فشل في تثبيت بعض المكتبات")
            return False
    
    return True

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['backups', 'reports', 'temp']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 تم إنشاء مجلد {directory}")

def check_database():
    """التحقق من قاعدة البيانات"""
    if not os.path.exists('flex_usa.db'):
        print("🗄️ قاعدة البيانات غير موجودة، سيتم إنشاؤها تلقائياً")
        
        # سؤال المستخدم عن البيانات التجريبية
        choice = input("هل تريد إضافة بيانات تجريبية؟ (y/n): ").lower().strip()
        
        if choice == 'y':
            print("🔄 جاري إعداد البيانات التجريبية...")
            try:
                import setup_demo_data
                setup_demo_data.setup_demo_data()
                print("✅ تم إعداد البيانات التجريبية")
            except Exception as e:
                print(f"❌ خطأ في إعداد البيانات التجريبية: {e}")
        else:
            # إنشاء قاعدة بيانات فارغة
            try:
                from database import DatabaseManager
                db = DatabaseManager()
                print("✅ تم إنشاء قاعدة بيانات جديدة")
            except Exception as e:
                print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
                return False
    else:
        print("✅ قاعدة البيانات موجودة")
    
    return True

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 جاري تشغيل نظام Flex USA المحاسبي...")
    print("=" * 50)
    
    try:
        from main import main
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات وإعادة المحاولة")

def main():
    """الدالة الرئيسية"""
    print("🎯 مرحباً بك في نظام Flex USA المحاسبي")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # إعداد المجلدات
    setup_directories()
    
    # التحقق من قاعدة البيانات
    if not check_database():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات جاهزة!")
    
    # تشغيل التطبيق
    start_application()

if __name__ == "__main__":
    main()

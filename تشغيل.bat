@echo off
chcp 65001 >nul
title Flex USA Accounting System

echo.
echo ================================================
echo    💼 نظام Flex USA المحاسبي الاحترافي
echo ================================================
echo.
echo 🌟 الميزات:
echo    • تصميم احترافي وعصري
echo    • دعم كامل للغة العربية
echo    • واجهة مستخدم محسنة
echo    • أمان وحماية عالية
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من المكتبات المطلوبة
echo 🔍 فحص المكتبات المطلوبة...
python -c "import PyQt5" 2>nul
if errorlevel 1 (
    echo ❌ PyQt5 غير مثبت
    echo 🔧 جاري تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

REM تشغيل النظام
echo.
echo 🎯 تشغيل النظام...
echo.
python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo 💡 تحقق من رسائل الخطأ أعلاه
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
)

echo.
echo 🎨 شكراً لاستخدام نظام Flex USA!
pause

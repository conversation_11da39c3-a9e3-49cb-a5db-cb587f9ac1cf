#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام Flex USA المحاسبي
"""

import sys
import os

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 بدء تشغيل نظام Flex USA المحاسبي")
        
        # استيراد المكتبات المطلوبة
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        print("✅ تم تحميل مكتبات PyQt5")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخط العربي
        font = QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(12)
        app.setFont(font)
        
        print("✅ تم إعداد التطبيق")
        
        # إنشاء المجلدات المطلوبة
        directories = ['assets', 'backups', 'reports', 'invoices', 'temp']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")
        
        # استيراد النافذة الرئيسية
        from main_window import MainWindow
        print("✅ تم تحميل النافذة الرئيسية")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # محاولة تسجيل الدخول
        print("🔐 فتح نافذة تسجيل الدخول...")
        if main_window.login():
            print("✅ تم تسجيل الدخول بنجاح")
            main_window.show()
            print("🎉 النظام جاهز للاستخدام!")
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("pip install PyQt5 reportlab arabic-reshaper python-bidi")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف النظام بواسطة المستخدم")
        sys.exit(0)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة طباعة الفواتير الجديدة
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_invoice_printing():
    """اختبار ميزة طباعة الفواتير الجديدة"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    print("🎯 اختبار ميزة طباعة الفواتير الجديدة")
    
    print("\n✨ الميزات الجديدة:")
    
    print("\n🖨️ زر طباعة الفاتورة:")
    print("   • استبدال زر 'عرض التفاصيل' بزر 'طباعة الفاتورة'")
    print("   • لون بنفسجي مميز (#9b59b6)")
    print("   • أيقونة طابعة واضحة 🖨️")
    print("   • نصيحة أداة: 'طباعة فاتورة العملية'")
    
    print("\n📄 الفاتورة المطبوعة تحتوي على:")
    print("   🏢 معلومات الشركة:")
    print("      - اسم الشركة (عربي وإنجليزي)")
    print("      - العنوان (عربي وإنجليزي)")
    print("      - أرقام الهاتف والجوال")
    print("      - البريد الإلكتروني والموقع")
    print("      - رقم الترخيص والرقم الضريبي")
    print("   ")
    print("   📋 معلومات الفاتورة:")
    print("      - رقم الفاتورة (رقم المرجع)")
    print("      - تاريخ الفاتورة")
    print("      - وقت الإصدار")
    print("      - نوع الفاتورة (استلام/تسليم)")
    print("   ")
    print("   💰 تفاصيل العملية:")
    print("      - نوع العملية (ملون)")
    print("      - العملة")
    print("      - المبلغ (مع رمز العملة)")
    print("      - سعر الصرف")
    print("      - التاريخ")
    print("      - اسم الجهة")
    print("      - رقم المرجع")
    print("      - الوصف")
    
    print("\n🏢 إعدادات الشركة:")
    print("   • نافذة إعدادات جديدة في قائمة 'ملف'")
    print("   • حقول شاملة لجميع معلومات الشركة:")
    print("     - اسم الشركة (عربي وإنجليزي)")
    print("     - العنوان (عربي وإنجليزي)")
    print("     - رقم الهاتف والجوال")
    print("     - البريد الإلكتروني")
    print("     - الموقع الإلكتروني")
    print("     - رقم الترخيص")
    print("     - الرقم الضريبي")
    print("   • حفظ الإعدادات في ملف config.json")
    print("   • تحميل تلقائي للإعدادات المحفوظة")
    
    print("\n🎨 تصميم الفاتورة:")
    print("   • رأس احترافي مع معلومات الشركة")
    print("   • جدول معلومات الفاتورة منسق")
    print("   • جدول تفاصيل العملية بألوان مميزة:")
    print("     - الاستلام: أخضر")
    print("     - التسليم: أحمر")
    print("     - المبلغ: خط عريض وحجم أكبر")
    print("   • ذيل مع رسالة شكر ومعلومات النظام")
    print("   • تخطيط A4 مع هوامش مناسبة")
    
    print("\n💾 حفظ وفتح الفواتير:")
    print("   • حفظ تلقائي في مجلد 'invoices'")
    print("   • اسم ملف فريد: invoice_[ID]_[timestamp].pdf")
    print("   • رسالة تأكيد مع خيار فتح الفاتورة")
    print("   • فتح تلقائي حسب نظام التشغيل:")
    print("     - Windows: start")
    print("     - macOS: open")
    print("     - Linux: xdg-open")
    
    print("\n🔧 التحسينات التقنية:")
    print("   • فئة InvoicePrinter منفصلة")
    print("   • استخدام مكتبة ReportLab للـ PDF")
    print("   • دعم الخطوط العربية")
    print("   • تخطيط احترافي مع جداول منسقة")
    print("   • معالجة أخطاء شاملة")
    print("   • إعدادات قابلة للتخصيص")
    
    print("\n🎯 ترتيب الأزرار الجديد:")
    print("   [🖨️ طباعة] [✏️ تعديل] [🗑️ حذف]")
    print("   من اليسار إلى اليمين حسب الاستخدام")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. اذهب إلى 'ملف' > 'إعدادات الشركة'")
    print("   3. أدخل معلومات شركتك")
    print("   4. احفظ الإعدادات")
    print("   5. أضف عملية مالية جديدة")
    print("   6. اضغط على زر 'طباعة الفاتورة' 🖨️")
    print("   7. اختر 'نعم' لفتح الفاتورة")
    print("   8. راجع الفاتورة المطبوعة")
    
    print("\n💡 نصائح الاستخدام:")
    print("   • أدخل معلومات الشركة كاملة للحصول على فاتورة احترافية")
    print("   • استخدم أرقام مراجع واضحة")
    print("   • تأكد من صحة بيانات العملية قبل الطباعة")
    print("   • احتفظ بنسخ من الفواتير للمراجعة")
    
    print("\n📁 الملفات والمجلدات:")
    print("   • config.json: إعدادات الشركة")
    print("   • invoices/: مجلد الفواتير المطبوعة")
    print("   • assets/: مجلد الشعارات والخطوط")
    print("   • invoice_printer.py: فئة طباعة الفواتير")
    
    print("\n🎨 الألوان المستخدمة:")
    print("   • زر الطباعة: #9b59b6 → #8e44ad (بنفسجي)")
    print("   • رأس الفاتورة: #2c3e50 (رمادي داكن)")
    print("   • الاستلام: #27ae60 (أخضر)")
    print("   • التسليم: #e74c3c (أحمر)")
    print("   • المبلغ: #2c3e50 (أسود)")
    
    print("\n⚡ الأداء:")
    print("   • إنشاء سريع للفواتير")
    print("   • ملفات PDF محسنة الحجم")
    print("   • لا تؤثر على أداء النظام")
    print("   • معالجة أخطاء فعالة")
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - جرب ميزة طباعة الفواتير!")
        print("🔥 لاحظ زر الطباعة البنفسجي الجديد في عمود الإجراءات!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_invoice_printing()

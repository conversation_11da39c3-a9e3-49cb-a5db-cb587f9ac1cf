#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة تسجيل الدخول
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_login_fix():
    """اختبار إصلاح مشكلة تسجيل الدخول"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Segoe UI")
    font.setPointSize(12)
    app.setFont(font)
    
    print("🔐 اختبار إصلاح مشكلة تسجيل الدخول")
    
    print("\n✨ الإصلاحات المطبقة:")
    
    print("\n🔧 إصلاح نافذة تسجيل الدخول:")
    print("   • إضافة التحقق من صحة بيانات المستخدم")
    print("   • إضافة دالة verify_credentials()")
    print("   • حفظ بيانات المستخدم في user_data")
    print("   • عرض رسائل خطأ واضحة")
    print("   • تأثيرات بصرية أثناء التحقق")
    
    print("\n🔧 إصلاح النافذة الرئيسية:")
    print("   • التحقق من user_data من نافذة تسجيل الدخول")
    print("   • تجنب التحقق المزدوج من بيانات المستخدم")
    print("   • تحديث عنوان النافذة بشكل صحيح")
    print("   • تحديث شريط الحالة")
    print("   • تحديث البيانات بعد تسجيل الدخول")
    
    print("\n📝 تدفق تسجيل الدخول الجديد:")
    print("   1. المستخدم يدخل اسم المستخدم وكلمة المرور")
    print("   2. الضغط على زر تسجيل الدخول")
    print("   3. عرض 'جاري التحقق...' مع تعطيل الزر")
    print("   4. التحقق من البيانات في قاعدة البيانات")
    print("   5. إذا كانت صحيحة: حفظ في user_data وإغلاق النافذة")
    print("   6. إذا كانت خاطئة: عرض رسالة خطأ")
    print("   7. النافذة الرئيسية تتحقق من user_data")
    print("   8. إذا وجدت: تسجيل دخول ناجح")
    print("   9. تحديث واجهة النظام")
    
    print("\n🔒 بيانات تسجيل الدخول الصحيحة:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print("\n❌ بيانات خاطئة للاختبار:")
    print("   اسم المستخدم: wrong")
    print("   كلمة المرور: wrong123")
    
    print("\n🎯 نقاط الاختبار:")
    print("   1. اختبار تسجيل دخول صحيح")
    print("   2. اختبار تسجيل دخول خاطئ")
    print("   3. اختبار حقول فارغة")
    print("   4. اختبار التأثيرات البصرية")
    print("   5. اختبار الانتقال للنافذة الرئيسية")
    
    print("\n💡 ما يجب ملاحظته:")
    print("   ✅ نافذة تسجيل الدخول تظهر بشكل صحيح")
    print("   ✅ رسالة 'جاري التحقق...' تظهر عند الضغط")
    print("   ✅ رسالة خطأ تظهر للبيانات الخاطئة")
    print("   ✅ النافذة الرئيسية تفتح للبيانات الصحيحة")
    print("   ✅ عنوان النافذة يتحدث باسم المستخدم")
    print("   ✅ شريط الحالة يظهر رسالة ترحيب")
    
    # إنشاء المجلدات المطلوبة
    directories = ['assets', 'backups', 'reports', 'invoices', 'temp']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    try:
        # استيراد النافذة الرئيسية
        from main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        print("\n🚀 بدء اختبار تسجيل الدخول...")
        
        # محاولة تسجيل الدخول
        if main_window.login():
            print("✅ تم تسجيل الدخول بنجاح!")
            print("🎉 النافذة الرئيسية ستظهر الآن")
            main_window.show()
            
            print("\n🔥 الاختبار نجح! النظام يعمل بشكل صحيح")
            print("💡 جرب الآن:")
            print("   • إضافة عملية مالية جديدة")
            print("   • طباعة فاتورة")
            print("   • تعديل أو حذف عملية")
            print("   • إعدادات الشركة")
            
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول أو فشل")
            print("💡 هذا طبيعي إذا ضغطت على إلغاء أو أدخلت بيانات خاطئة")
            return 0
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = test_login_fix()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)

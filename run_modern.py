#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام Flex USA المحاسبي - الإصدار الحديث والاحترافي
"""

import sys
import os
import warnings

# إخفاء التحذيرات غير المهمة
warnings.filterwarnings("ignore")
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 تشغيل نظام Flex USA المحاسبي - الإصدار الحديث")
        print("🎨 تطبيق التصميم الاحترافي الجديد...")
        print("🔐 معلومات تسجيل الدخول: admin / admin123")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        from modern_theme import ModernTheme
        from modern_components import ModernLoginDialog

        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # تطبيق النظام الموحد للخطوط
        fonts = ModernTheme.apply_fonts(app)
        
        # تطبيق الستايل الرئيسي
        app.setStyleSheet(ModernTheme.get_main_stylesheet())

        # إنشاء المجلدات
        for directory in ['assets', 'backups', 'reports', 'invoices', 'temp']:
            if not os.path.exists(directory):
                os.makedirs(directory)

        print("✅ تم تطبيق التصميم الحديث بنجاح")

        # عرض شاشة تسجيل الدخول الحديثة
        login_dialog = ModernLoginDialog()
        
        if login_dialog.exec_() == login_dialog.Accepted:
            user_data = login_dialog.get_user_data()
            print(f"✅ تم تسجيل الدخول بنجاح للمستخدم: {user_data['username']}")
            
            # الآن يمكن استيراد النافذة الرئيسية بأمان
            from main_window import MainWindow
            
            # إنشاء النافذة الرئيسية
            main_window = MainWindow()
            main_window.current_user = user_data
            
            # تطبيق التصميم الحديث على النافذة الرئيسية
            main_window.setStyleSheet(ModernTheme.get_main_stylesheet())
            
            main_window.show()
            print("🎉 النظام جاهز للاستخدام!")
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0

    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

import os
from datetime import datetime, date, timedelta
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill

class ReportGenerator:
    def __init__(self, db_manager):
        self.db = db_manager
        
    def generate_daily_report(self, report_date=None, export_format='pdf'):
        """إنشاء تقرير يومي"""
        if not report_date:
            report_date = date.today()
            
        # الحصول على العمليات لليوم المحدد
        transactions = self.db.get_transactions(
            start_date=report_date.strftime('%Y-%m-%d'),
            end_date=report_date.strftime('%Y-%m-%d')
        )
        
        if export_format.lower() == 'pdf':
            return self._generate_pdf_report(transactions, f"تقرير يومي - {report_date.strftime('%Y-%m-%d')}")
        elif export_format.lower() == 'excel':
            return self._generate_excel_report(transactions, f"تقرير يومي - {report_date.strftime('%Y-%m-%d')}")
    
    def generate_monthly_report(self, year=None, month=None, export_format='pdf'):
        """إنشاء تقرير شهري"""
        if not year:
            year = date.today().year
        if not month:
            month = date.today().month
            
        # حساب أول وآخر يوم في الشهر
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        # الحصول على العمليات للشهر المحدد
        transactions = self.db.get_transactions(
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        if export_format.lower() == 'pdf':
            return self._generate_pdf_report(transactions, f"تقرير شهري - {year}/{month:02d}")
        elif export_format.lower() == 'excel':
            return self._generate_excel_report(transactions, f"تقرير شهري - {year}/{month:02d}")
    
    def generate_currency_report(self, currency, start_date=None, end_date=None, export_format='pdf'):
        """إنشاء تقرير حسب العملة"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
            
        # الحصول على العمليات للعملة المحددة
        transactions = self.db.get_transactions(
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d'),
            currency=currency
        )
        
        title = f"تقرير {currency} - من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"
        
        if export_format.lower() == 'pdf':
            return self._generate_pdf_report(transactions, title)
        elif export_format.lower() == 'excel':
            return self._generate_excel_report(transactions, title)
    
    def _generate_pdf_report(self, transactions, title):
        """إنشاء تقرير PDF"""
        filename = f"reports/{title.replace('/', '-')}.pdf"
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs("reports", exist_ok=True)
        
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        # العنوان
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 20))
        
        # إحصائيات سريعة
        stats = self._calculate_statistics(transactions)
        stats_data = [
            ['الإحصائيات', ''],
            ['إجمالي العمليات', str(len(transactions))],
            ['إجمالي المستلم (دينار)', f"{stats['lyd_received']:.2f}"],
            ['إجمالي المسلم (دينار)', f"{stats['lyd_paid']:.2f}"],
            ['صافي الدينار', f"{stats['lyd_net']:.2f}"],
            ['إجمالي المستلم (دولار)', f"{stats['usd_received']:.2f}"],
            ['إجمالي المسلم (دولار)', f"{stats['usd_paid']:.2f}"],
            ['صافي الدولار', f"{stats['usd_net']:.2f}"],
        ]
        
        stats_table = Table(stats_data, colWidths=[200, 200])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 30))
        
        # جدول العمليات
        if transactions:
            data = [['التاريخ', 'النوع', 'العملة', 'المبلغ', 'الجهة', 'المرجع']]
            
            for transaction in transactions:
                data.append([
                    str(transaction[5]),  # التاريخ
                    transaction[1],       # النوع
                    transaction[2],       # العملة
                    f"{transaction[3]:.2f}",  # المبلغ
                    transaction[6],       # الجهة
                    transaction[7] or ""  # المرجع
                ])
            
            table = Table(data, colWidths=[80, 60, 80, 80, 120, 80])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("لا توجد عمليات في الفترة المحددة", styles['Normal']))
        
        # إنشاء PDF
        doc.build(story)
        return filename
    
    def _generate_excel_report(self, transactions, title):
        """إنشاء تقرير Excel"""
        filename = f"reports/{title.replace('/', '-')}.xlsx"
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs("reports", exist_ok=True)
        
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "التقرير"
        
        # العنوان
        worksheet['A1'] = title
        worksheet['A1'].font = Font(size=16, bold=True)
        worksheet['A1'].alignment = Alignment(horizontal='center')
        worksheet.merge_cells('A1:F1')
        
        # الإحصائيات
        stats = self._calculate_statistics(transactions)
        row = 3
        worksheet[f'A{row}'] = "الإحصائيات"
        worksheet[f'A{row}'].font = Font(bold=True)
        
        stats_data = [
            ('إجمالي العمليات', len(transactions)),
            ('إجمالي المستلم (دينار)', stats['lyd_received']),
            ('إجمالي المسلم (دينار)', stats['lyd_paid']),
            ('صافي الدينار', stats['lyd_net']),
            ('إجمالي المستلم (دولار)', stats['usd_received']),
            ('إجمالي المسلم (دولار)', stats['usd_paid']),
            ('صافي الدولار', stats['usd_net']),
        ]
        
        for i, (label, value) in enumerate(stats_data):
            worksheet[f'A{row + 1 + i}'] = label
            worksheet[f'B{row + 1 + i}'] = value
        
        # جدول العمليات
        row = row + len(stats_data) + 3
        headers = ['التاريخ', 'النوع', 'العملة', 'المبلغ', 'الجهة', 'المرجع', 'الوصف']
        
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # بيانات العمليات
        for i, transaction in enumerate(transactions):
            row_data = [
                str(transaction[5]),  # التاريخ
                transaction[1],       # النوع
                transaction[2],       # العملة
                transaction[3],       # المبلغ
                transaction[6],       # الجهة
                transaction[7] or "", # المرجع
                transaction[8] or ""  # الوصف
            ]
            
            for col, value in enumerate(row_data, 1):
                worksheet.cell(row=row + 1 + i, column=col, value=value)
        
        # حفظ الملف
        workbook.save(filename)
        return filename
    
    def _calculate_statistics(self, transactions):
        """حساب الإحصائيات للعمليات"""
        stats = {
            'lyd_received': 0,
            'lyd_paid': 0,
            'lyd_net': 0,
            'usd_received': 0,
            'usd_paid': 0,
            'usd_net': 0
        }
        
        for transaction in transactions:
            currency = transaction[2]
            amount = transaction[3]
            transaction_type = transaction[1]
            
            if currency == 'دينار ليبي':
                if transaction_type == 'استلام':
                    stats['lyd_received'] += amount
                else:
                    stats['lyd_paid'] += amount
            elif currency == 'دولار أمريكي':
                if transaction_type == 'استلام':
                    stats['usd_received'] += amount
                else:
                    stats['usd_paid'] += amount
        
        stats['lyd_net'] = stats['lyd_received'] - stats['lyd_paid']
        stats['usd_net'] = stats['usd_received'] - stats['usd_paid']
        
        return stats

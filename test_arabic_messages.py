#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الرسائل العربية الجديدة مع أزرار "نعم - لا" و "موافق"
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_arabic_messages():
    """اختبار الرسائل العربية الجديدة"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    print("🎯 تم تعديل جميع نوافذ التنبيه لتكون باللغة العربية!")
    
    print("\n📋 التحديثات المطبقة:")
    
    print("\n🔄 رسائل التأكيد (نعم/لا):")
    print("   • تسجيل الخروج: 'نعم' بدلاً من 'Yes' و 'لا' بدلاً من 'No'")
    print("   • تأكيد استرجاع النسخة الاحتياطية: أزرار عربية")
    print("   • تأكيد حذف النسخة الاحتياطية: أزرار عربية")
    print("   • جميع رسائل التأكيد الأخرى: أزرار عربية")
    
    print("\n✅ رسائل المعلومات (موافق):")
    print("   • رسائل النجاح: زر 'موافق' بدلاً من 'OK'")
    print("   • رسائل المعلومات: زر 'موافق' عربي")
    print("   • إشعارات النظام: أزرار عربية")
    
    print("\n⚠️ رسائل التحذير (موافق):")
    print("   • تحذيرات البيانات: زر 'موافق' عربي")
    print("   • تحذيرات النظام: أزرار عربية")
    print("   • رسائل التنبيه: تصميم عربي كامل")
    
    print("\n❌ رسائل الأخطاء (موافق):")
    print("   • رسائل الأخطاء: زر 'موافق' عربي")
    print("   • أخطاء النظام: تصميم عربي")
    print("   • رسائل الفشل: أزرار عربية")
    
    print("\n🎨 الميزات الجديدة:")
    print("   • اتجاه RTL كامل لجميع الرسائل")
    print("   • أيقونات معبرة في عناوين الرسائل")
    print("   • ألوان مميزة لكل نوع رسالة:")
    print("     - أخضر للنجاح والمعلومات")
    print("     - برتقالي للتحذيرات")
    print("     - أحمر للأخطاء")
    print("     - أزرق للتأكيد")
    print("   • خطوط عربية واضحة ومقروءة")
    print("   • تأثيرات hover تفاعلية")
    
    print("\n🔧 الملفات المحدثة:")
    print("   • main_window.py - الرسائل الرئيسية")
    print("   • backup_manager.py - رسائل النسخ الاحتياطية")
    print("   • transaction_dialog.py - رسائل العمليات المالية")
    print("   • login_dialog.py - رسائل تسجيل الدخول")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. جرب تسجيل الخروج لرؤية رسالة 'نعم/لا'")
    print("   3. افتح إدارة النسخ الاحتياطية واختبر الرسائل")
    print("   4. أضف عملية مالية جديدة واختبر رسائل التحذير")
    print("   5. جرب إدارة المستخدمين واختبر الرسائل")
    
    print("\n💡 نصائح الاختبار:")
    print("   • لاحظ الأزرار العربية في جميع الرسائل")
    print("   • تحقق من الاتجاه RTL للنصوص")
    print("   • جرب الألوان المختلفة للرسائل")
    print("   • اختبر تأثيرات hover على الأزرار")
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - اختبر الرسائل العربية الجديدة!")
        print("🔥 جرب تسجيل الخروج لرؤية أول رسالة عربية!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_arabic_messages()

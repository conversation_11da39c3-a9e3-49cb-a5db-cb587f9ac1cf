#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأيقونات الجديدة في القوائم والشريط العلوي
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_new_icons():
    """اختبار الأيقونات الجديدة"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    print("🎯 تم إضافة الأيقونات المعبرة لجميع القوائم!")
    
    print("\n📋 القوائم الرئيسية مع الأيقونات:")
    print("   📁 ملف")
    print("      ➕ عملية جديدة")
    print("      💾 نسخة احتياطية")
    print("      👥 إدارة المستخدمين والصلاحيات")
    print("      🚪 خروج")
    
    print("\n   📊 التقارير")
    print("      📅 تقرير يومي")
    print("      📈 تقرير شهري")
    
    print("\n   ⚙️ إعدادات")
    print("      💱 أسعار الصرف")
    
    print("\n   🆘 المساعدة")
    print("      📖 دليل المستخدم")
    print("      ⌨️ اختصارات لوحة المفاتيح")
    print("      ℹ️ حول البرنامج")
    
    print("\n   🚪 الخروج")
    print("      🔓 تسجيل خروج")
    print("      ❌ إغلاق البرنامج")
    
    print("\n🛠️ شريط الأدوات المحدث:")
    print("   ➕ عملية جديدة")
    print("   🔄 تحديث")
    print("   📊 تقرير سريع")
    print("   💾 نسخة احتياطية")
    print("   👥 المستخدمين")
    print("   🆘 مساعدة")
    
    print("\n✨ الميزات الجديدة:")
    print("   • أيقونات معبرة وواضحة لجميع القوائم")
    print("   • شريط أدوات محسن مع أزرار إضافية")
    print("   • تصميم متسق ومتناغم")
    print("   • سهولة التعرف على الوظائف")
    print("   • واجهة أكثر احترافية وجاذبية")
    
    print("\n🔑 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. لاحظ الأيقونات في الشريط العلوي")
    print("   3. جرب القوائم المختلفة")
    print("   4. استخدم شريط الأدوات المحسن")
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - استمتع بالواجهة الجديدة!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_new_icons()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل دخول احترافية ومتطورة لنظام Flex USA
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
import sys

class ProfessionalLoginScreen(QDialog):
    """شاشة تسجيل دخول احترافية مع تصميم متطور"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_login_screen()
        self.create_animations()
    
    def setup_login_screen(self):
        """إعداد شاشة تسجيل الدخول"""
        # إعدادات النافذة
        self.setWindowTitle("Flex USA - Professional Login")
        self.setFixedSize(1000, 650)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الجانب الأيسر (معلومات النظام)
        self.create_info_panel(main_layout)
        
        # إنشاء الجانب الأيمن (نموذج تسجيل الدخول)
        self.create_login_panel(main_layout)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.connect_events()
        
        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(100, lambda: self.username_input.setFocus())
    
    def create_info_panel(self, main_layout):
        """إنشاء لوحة المعلومات الجانبية"""
        info_widget = QWidget()
        info_widget.setFixedWidth(500)
        info_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:0.5 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
                border-top-left-radius: 15px;
                border-bottom-left-radius: 15px;
            }}
        """)
        
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(60, 80, 60, 80)
        info_layout.setSpacing(30)
        
        # شعار النظام
        logo_container = QWidget()
        logo_layout = QVBoxLayout()
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # أيقونة كبيرة
        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 80px;
                color: white;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        logo_layout.addWidget(logo_label)
        
        logo_container.setLayout(logo_layout)
        info_layout.addWidget(logo_container)
        
        # عنوان النظام
        title_label = QLabel("نظام Flex USA")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: white;
                margin-bottom: 10px;
            }
        """)
        info_layout.addWidget(title_label)
        
        # وصف النظام
        subtitle_label = QLabel("نظام المحاسبة المالي الاحترافي")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: 40px;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        # قائمة المميزات
        features_widget = self.create_features_list()
        info_layout.addWidget(features_widget)
        
        # مساحة مرنة
        info_layout.addStretch()
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0 - 2024")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
            }
        """)
        info_layout.addWidget(version_label)
        
        info_widget.setLayout(info_layout)
        main_layout.addWidget(info_widget)
    
    def create_features_list(self):
        """إنشاء قائمة مميزات النظام"""
        features_widget = QWidget()
        features_layout = QVBoxLayout()
        features_layout.setSpacing(15)
        
        features = [
            ("🔒", "أمان وحماية عالية"),
            ("📊", "تقارير مالية شاملة"),
            ("💱", "دعم عملات متعددة"),
            ("📱", "واجهة عصرية وسهلة"),
            ("☁️", "نسخ احتياطية تلقائية")
        ]
        
        for icon, text in features:
            feature_widget = QWidget()
            feature_layout = QHBoxLayout()
            feature_layout.setContentsMargins(0, 0, 0, 0)
            
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("""
                QLabel {
                    font-size: 20px;
                    color: white;
                    min-width: 30px;
                }
            """)
            
            text_label = QLabel(text)
            text_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.9);
                }
            """)
            
            feature_layout.addWidget(icon_label)
            feature_layout.addWidget(text_label)
            feature_layout.addStretch()
            
            feature_widget.setLayout(feature_layout)
            features_layout.addWidget(feature_widget)
        
        features_widget.setLayout(features_layout)
        return features_widget

    def create_login_panel(self, main_layout):
        """إنشاء لوحة تسجيل الدخول"""
        login_widget = QWidget()
        login_widget.setFixedWidth(500)
        login_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernTheme.WHITE};
                border-top-right-radius: 15px;
                border-bottom-right-radius: 15px;
            }}
        """)

        login_layout = QVBoxLayout()
        login_layout.setContentsMargins(80, 60, 80, 60)
        login_layout.setSpacing(25)

        # زر الإغلاق
        close_button = QPushButton("✕")
        close_button.setFixedSize(35, 35)
        close_button.clicked.connect(self.reject)
        close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: none;
                border-radius: 17px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.WARNING_RED};
                color: white;
            }}
        """)

        # تخطيط الرأس
        header_layout = QHBoxLayout()
        header_layout.addStretch()
        header_layout.addWidget(close_button)
        login_layout.addLayout(header_layout)

        # مساحة مرنة
        login_layout.addStretch()

        # عنوان تسجيل الدخول
        welcome_label = QLabel("مرحباً بك!")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                font-weight: bold;
                color: {ModernTheme.HEADER_DARK};
                margin-bottom: 10px;
            }}
        """)
        login_layout.addWidget(welcome_label)

        # وصف تسجيل الدخول
        desc_label = QLabel("يرجى تسجيل الدخول للوصول إلى النظام")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: 30px;
            }}
        """)
        login_layout.addWidget(desc_label)

        # نموذج تسجيل الدخول
        form_widget = self.create_login_form()
        login_layout.addWidget(form_widget)

        # مساحة مرنة
        login_layout.addStretch()

        # معلومات تجريبية
        demo_info = self.create_demo_info()
        login_layout.addWidget(demo_info)

        login_widget.setLayout(login_layout)
        main_layout.addWidget(login_widget)

    def create_login_form(self):
        """إنشاء نموذج تسجيل الدخول"""
        form_widget = QWidget()
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)

        # حقل اسم المستخدم
        username_container = self.create_input_field(
            "👤", "اسم المستخدم", "أدخل اسم المستخدم"
        )
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)

        # حقل كلمة المرور
        password_container = self.create_input_field(
            "🔒", "كلمة المرور", "أدخل كلمة المرور", is_password=True
        )
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)

        # خيار تذكر المستخدم
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet(f"""
            QCheckBox {{
                font-size: 12px;
                color: {ModernTheme.TEXT_SECONDARY};
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
            }}
            QCheckBox::indicator:checked {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
        """)

        forgot_link = QLabel('<a href="#" style="color: #3498DB; text-decoration: none;">نسيت كلمة المرور؟</a>')
        forgot_link.setAlignment(Qt.AlignRight)
        forgot_link.setStyleSheet("font-size: 12px;")

        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        remember_layout.addWidget(forgot_link)
        form_layout.addLayout(remember_layout)

        # أزرار العمل
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFixedHeight(50)
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:1 {ModernTheme.PRIMARY_HOVER});
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.HEADER_DARK};
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.SECONDARY_GRAY};
            }}
        """)
        buttons_layout.addWidget(self.login_button)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFixedHeight(45)
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 22px;
                font-size: 14px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                border-color: {ModernTheme.SECONDARY_GRAY};
            }}
        """)
        buttons_layout.addWidget(cancel_button)

        form_layout.addLayout(buttons_layout)
        form_widget.setLayout(form_layout)
        return form_widget

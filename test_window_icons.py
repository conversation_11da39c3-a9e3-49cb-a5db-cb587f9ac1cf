#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأيقونات الجديدة في شريط العنوان لجميع النوافذ
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_window_icons():
    """اختبار الأيقونات الجديدة في شريط العنوان"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    print("🎯 تم إضافة الأيقونات المعبرة لشريط العنوان في جميع النوافذ!")
    
    print("\n🪟 النوافذ المحدثة مع الأيقونات:")
    
    print("\n📱 النوافذ الرئيسية:")
    print("   💰 نظام Flex USA المحاسبي (النافذة الرئيسية)")
    print("   🔐 تسجيل الدخول - Flex USA")
    
    print("\n💼 نوافذ العمليات المالية:")
    print("   💰 إضافة عملية مالية جديدة")
    print("   💱 إدارة أسعار الصرف")
    
    print("\n🛠️ نوافذ الإدارة:")
    print("   👥 إدارة المستخدمين والصلاحيات")
    print("   💾 إدارة النسخ الاحتياطية")
    
    print("\n🆘 نوافذ المساعدة:")
    print("   📖 دليل المستخدم - نظام Flex USA")
    print("   ⌨️ اختصارات لوحة المفاتيح")
    print("   ℹ️ حول البرنامج")
    
    print("\n🎨 دليل الأيقونات المستخدمة:")
    print("   💰 - العمليات المالية والنظام الرئيسي")
    print("   🔐 - الأمان وتسجيل الدخول")
    print("   👥 - إدارة المستخدمين")
    print("   💾 - النسخ الاحتياطية والحفظ")
    print("   💱 - أسعار الصرف والعملات")
    print("   📖 - الدليل والمساعدة")
    print("   ⌨️ - اختصارات لوحة المفاتيح")
    print("   ℹ️ - المعلومات والتفاصيل")
    
    print("\n✨ الفوائد الجديدة:")
    print("   • تمييز سريع للنوافذ من شريط المهام")
    print("   • واجهة أكثر احترافية ووضوحاً")
    print("   • سهولة التعرف على نوع النافذة")
    print("   • تصميم متسق عبر جميع النوافذ")
    print("   • تحسين تجربة المستخدم البصرية")
    
    print("\n🔧 الملفات المحدثة:")
    print("   • main_window.py - النافذة الرئيسية ونوافذ المساعدة")
    print("   • login_dialog.py - نافذة تسجيل الدخول")
    print("   • transaction_dialog.py - نوافذ العمليات المالية")
    print("   • backup_manager.py - نافذة إدارة النسخ الاحتياطية")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. لاحظ الأيقونة في شريط عنوان النافذة الرئيسية")
    print("   3. افتح النوافذ المختلفة من القوائم")
    print("   4. لاحظ الأيقونات المميزة لكل نافذة")
    print("   5. تحقق من شريط المهام لرؤية الأيقونات")
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - استمتع بالأيقونات الجديدة!")
        print("💡 نصيحة: افتح عدة نوافذ لرؤية الأيقونات المختلفة في شريط المهام")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_window_icons()

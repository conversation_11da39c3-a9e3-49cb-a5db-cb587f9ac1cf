#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام الحديث
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_modern_system():
    """اختبار النظام الحديث"""
    try:
        print("🧪 اختبار النظام الحديث...")
        
        # اختبار استيراد المكونات
        print("📦 اختبار استيراد المكونات...")
        from modern_theme import ModernTheme
        from modern_components import ModernLoginDialog, ModernButton, ModernCard
        print("✅ تم استيراد المكونات بنجاح")
        
        # اختبار PyQt5
        print("🖼️ اختبار PyQt5...")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار الستايل
        print("🎨 اختبار الستايل...")
        stylesheet = ModernTheme.get_main_stylesheet()
        app.setStyleSheet(stylesheet)
        print("✅ تم تطبيق الستايل بنجاح")
        
        # اختبار شاشة تسجيل الدخول
        print("🔐 اختبار شاشة تسجيل الدخول...")
        login_dialog = ModernLoginDialog()
        print("✅ تم إنشاء شاشة تسجيل الدخول بنجاح")
        
        # عرض الشاشة
        print("👁️ عرض شاشة تسجيل الدخول...")
        login_dialog.show()
        
        print("🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن إغلاق النافذة لإنهاء الاختبار")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_modern_system())

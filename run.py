#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف التشغيل الرئيسي لنظام Flex USA المحاسبي
"""

import sys
import os
import warnings

# إخفاء التحذيرات غير المهمة
warnings.filterwarnings("ignore")
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        print("🚀 تشغيل نظام Flex USA المحاسبي")
        print("🎨 تطبيق التصميم الاحترافي...")
        print("🔐 معلومات تسجيل الدخول: admin / admin123")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modern_theme import ModernTheme

        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # تطبيق النظام الموحد للخطوط والألوان
        ModernTheme.apply_fonts(app)
        app.setStyleSheet(ModernTheme.get_main_stylesheet())

        # إنشاء المجلدات المطلوبة
        for directory in ['assets', 'backups', 'reports', 'invoices', 'temp']:
            if not os.path.exists(directory):
                os.makedirs(directory)

        print("✅ تم تطبيق التصميم الحديث بنجاح")

        # استيراد وتشغيل النافذة الرئيسية
        from main_window import MainWindow
        
        main_window = MainWindow()
        main_window.setStyleSheet(ModernTheme.get_main_stylesheet())
        
        # محاولة تسجيل الدخول
        if main_window.login():
            main_window.show()
            print("🎉 النظام جاهز للاستخدام!")
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0

    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

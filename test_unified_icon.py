#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأيقونة الموحدة لجميع نوافذ النظام
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def test_unified_icon():
    """اختبار الأيقونة الموحدة لجميع نوافذ النظام"""
    
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Arial")
    font.setPointSize(12)
    app.setFont(font)
    
    print("🎯 اختبار الأيقونة الموحدة لجميع نوافذ النظام")
    
    print("\n✨ الميزة الجديدة:")
    print("   🎨 أيقونة موحدة لجميع نوافذ النظام ونوافذ التنبيه")
    print("   🔧 إدارة مركزية للأيقونات")
    print("   📱 مظهر احترافي ومتسق")
    print("   🎯 تعرف سريع على نوافذ النظام")
    
    print("\n🏗️ مدير الأيقونات (IconManager):")
    print("   📁 ملف منفصل: icon_manager.py")
    print("   🔍 البحث التلقائي عن الأيقونات:")
    print("      - assets/icon.png")
    print("      - assets/logo.png")
    print("      - assets/app_icon.png")
    print("      - icon.png")
    print("      - logo.png")
    print("   🎨 إنشاء أيقونة افتراضية إذا لم توجد")
    print("   ⚙️ تطبيق تلقائي على جميع النوافذ")
    
    print("\n🎨 الأيقونة الافتراضية:")
    print("   📐 الحجم: 64x64 بكسل")
    print("   🔵 الشكل: دائرة زرقاء مع حدود بنفسجية")
    print("   🔤 النص: حرف 'F' أبيض عريض")
    print("   🎨 الألوان:")
    print("      - الخلفية: #667eea (أزرق)")
    print("      - الحدود: #764ba2 (بنفسجي)")
    print("      - النص: #ffffff (أبيض)")
    
    print("\n🪟 النوافذ المحدثة:")
    print("   🏠 النوافذ الرئيسية:")
    print("      • النافذة الرئيسية للنظام")
    print("      • نافذة تسجيل الدخول")
    print("   ")
    print("   💼 نوافذ العمليات:")
    print("      • نافذة إضافة عملية مالية")
    print("      • نافذة تعديل العملية")
    print("      • نافذة إدارة أسعار الصرف")
    print("   ")
    print("   🛠️ نوافذ الإدارة:")
    print("      • نافذة إعدادات الشركة")
    print("      • نافذة إدارة النسخ الاحتياطية")
    print("   ")
    print("   💬 نوافذ التنبيه:")
    print("      • رسائل التأكيد (نعم/لا)")
    print("      • رسائل المعلومات (موافق)")
    print("      • رسائل التحذير (موافق)")
    print("      • رسائل الأخطاء (موافق)")
    
    print("\n🔧 التطبيق التقني:")
    print("   📝 الكود المضاف:")
    print("      from icon_manager import icon_manager")
    print("      icon_manager.apply_to_window(self)")
    print("      icon_manager.apply_to_message_box(msg_box)")
    print("   ")
    print("   🎯 الدوال المستخدمة:")
    print("      • apply_to_window(): للنوافذ العادية")
    print("      • apply_to_message_box(): لنوافذ الرسائل")
    print("      • get_app_icon(): للحصول على الأيقونة")
    
    print("\n✨ الفوائد المحققة:")
    print("   🎨 مظهر احترافي موحد:")
    print("      • جميع النوافذ لها نفس الأيقونة")
    print("      • تعرف سريع على نوافذ النظام")
    print("      • مظهر متسق في شريط المهام")
    print("   ")
    print("   🔧 سهولة الإدارة:")
    print("      • تغيير الأيقونة من مكان واحد")
    print("      • إضافة أيقونات جديدة بسهولة")
    print("      • إدارة مركزية للأيقونات")
    print("   ")
    print("   📱 تجربة مستخدم محسنة:")
    print("      • تمييز نوافذ النظام في شريط المهام")
    print("      • مظهر احترافي ومنظم")
    print("      • هوية بصرية موحدة")
    
    print("\n🎯 مسارات الأيقونات المدعومة:")
    print("   1. assets/icon.png (الأولوية الأولى)")
    print("   2. assets/logo.png (الأولوية الثانية)")
    print("   3. assets/app_icon.png (الأولوية الثالثة)")
    print("   4. icon.png (في المجلد الرئيسي)")
    print("   5. logo.png (في المجلد الرئيسي)")
    print("   6. أيقونة افتراضية (إذا لم توجد)")
    
    print("\n📁 هيكل الملفات:")
    print("   📂 المشروع/")
    print("   ├── 📄 icon_manager.py (مدير الأيقونات)")
    print("   ├── 📄 main_window.py (محدث)")
    print("   ├── 📄 login_dialog.py (محدث)")
    print("   ├── 📄 transaction_dialog.py (محدث)")
    print("   ├── 📄 backup_manager.py (محدث)")
    print("   └── 📂 assets/")
    print("       ├── 🖼️ icon.png (اختياري)")
    print("       ├── 🖼️ logo.png (اختياري)")
    print("       └── 🖼️ app_icon.png (اختياري)")
    
    print("\n🚀 للاختبار:")
    print("   1. قم بتسجيل الدخول (admin/admin123)")
    print("   2. لاحظ الأيقونة في شريط العنوان")
    print("   3. افتح النوافذ المختلفة من القوائم")
    print("   4. تحقق من الأيقونة في شريط المهام")
    print("   5. جرب نوافذ التنبيه والرسائل")
    print("   6. لاحظ التناسق في جميع النوافذ")
    
    print("\n💡 نصائح للتخصيص:")
    print("   🖼️ لإضافة أيقونة مخصصة:")
    print("      1. ضع ملف الأيقونة في مجلد assets/")
    print("      2. اسمه icon.png أو logo.png")
    print("      3. الحجم المفضل: 64x64 أو أكبر")
    print("      4. التنسيق: PNG مع شفافية")
    print("   ")
    print("   🔧 لتغيير الأيقونة الافتراضية:")
    print("      1. عدل دالة create_default_icon()")
    print("      2. غير الألوان والنص")
    print("      3. احفظ وأعد تشغيل النظام")
    
    print("\n🎨 الألوان المستخدمة:")
    print("   • الخلفية: #667eea (أزرق النظام)")
    print("   • الحدود: #764ba2 (بنفسجي النظام)")
    print("   • النص: #ffffff (أبيض)")
    print("   • الشفافية: مدعومة للـ PNG")
    
    print("\n⚡ الأداء:")
    print("   • تحميل واحد للأيقونة")
    print("   • استخدام مشترك عبر النظام")
    print("   • لا تأثير على الأداء")
    print("   • ذاكرة محسنة")
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    
    # محاولة تسجيل الدخول
    if main_window.login():
        main_window.show()
        print("\n✅ تم تسجيل الدخول بنجاح - لاحظ الأيقونة الموحدة!")
        print("🔥 جرب فتح النوافذ المختلفة ولاحظ الأيقونة في كل منها!")
        sys.exit(app.exec_())
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        sys.exit(0)

if __name__ == "__main__":
    test_unified_icon()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام Flex USA المحاسبي مع تفاصيل التشخيص
"""

import sys
import os
import traceback

def check_file_exists(filename):
    """فحص وجود ملف"""
    exists = os.path.exists(filename)
    print(f"{'✅' if exists else '❌'} {filename}: {'موجود' if exists else 'مفقود'}")
    return exists

def main():
    """الدالة الرئيسية مع تشخيص مفصل"""
    print("🔍 تشخيص نظام Flex USA المحاسبي")
    print("=" * 50)
    
    # فحص الملفات الأساسية
    print("\n📁 فحص الملفات الأساسية:")
    required_files = [
        'main_window.py',
        'login_dialog.py', 
        'database.py',
        'unified_theme.py',
        'icon_manager.py',
        'config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not check_file_exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return 1
    
    # فحص المكتبات
    print("\n📦 فحص المكتبات:")
    try:
        import PyQt5
        print("✅ PyQt5: متوفر")
    except ImportError:
        print("❌ PyQt5: غير متوفر")
        return 1
    
    try:
        import reportlab
        print("✅ ReportLab: متوفر")
    except ImportError:
        print("⚠️ ReportLab: غير متوفر (ميزة الطباعة ستكون محدودة)")
    
    try:
        import arabic_reshaper
        print("✅ Arabic Reshaper: متوفر")
    except ImportError:
        print("⚠️ Arabic Reshaper: غير متوفر (النصوص العربية قد لا تظهر بشكل صحيح)")
    
    # بدء التطبيق
    print("\n🚀 بدء تشغيل التطبيق:")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ تم استيراد PyQt5")
        
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication")
        
        # إعداد اللغة العربية
        app.setLayoutDirection(Qt.RightToLeft)
        print("✅ تم إعداد الاتجاه RTL")
        
        # إعداد الخط
        font = QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(12)
        app.setFont(font)
        print("✅ تم إعداد الخط")
        
        # إنشاء المجلدات
        directories = ['assets', 'backups', 'reports', 'invoices', 'temp']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")
        
        # استيراد النافذة الرئيسية
        print("\n📥 استيراد النافذة الرئيسية...")
        from main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        # إنشاء النافذة الرئيسية
        print("🏗️ إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء MainWindow")
        
        # محاولة تسجيل الدخول
        print("\n🔐 فتح نافذة تسجيل الدخول...")
        login_result = main_window.login()
        
        if login_result:
            print("✅ تم تسجيل الدخول بنجاح")
            main_window.show()
            print("🎉 النظام جاهز للاستخدام!")
            print("\n💡 معلومات تسجيل الدخول:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("\n🔥 استمتع بالنظام الموحد الجديد!")
            
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        traceback.print_exc()
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        print(f"\n🏁 انتهى التطبيق برمز الخروج: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف النظام بواسطة المستخدم (Ctrl+C)")
        sys.exit(0)
    except Exception as e:
        print(f"\n\n💥 خطأ فادح: {e}")
        traceback.print_exc()
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول المتكاملة لنظام Flex USA المحاسبي
متوافقة تماماً مع modern_components و ModernTheme
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
from modern_components import *
from icon_manager import icon_manager

class IntegratedLoginDialog(ModernDialog):
    """شاشة تسجيل دخول متكاملة مع النظام"""
    
    def __init__(self, parent=None):
        super().__init__("🔐 تسجيل الدخول - Flex USA", parent)
        self.user_data = None
        self.setup_integrated_login()
        self.create_smooth_animations()
    
    def setup_integrated_login(self):
        """إعداد شاشة تسجيل الدخول المتكاملة"""
        # إعدادات النافذة
        self.setFixedSize(750, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الجانب الأيمن (معلومات النظام)
        self.create_system_info_card(main_layout)
        
        # إنشاء الجانب الأيسر (نموذج تسجيل الدخول)
        self.create_login_form_card(main_layout)
        
        self.setLayout(main_layout)
        
        # ربط الأحداث
        self.connect_integrated_events()
        
        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(100, lambda: self.username_input.setFocus())
    
    def create_system_info_card(self, main_layout):
        """إنشاء بطاقة معلومات النظام"""
        info_card = QFrame()
        info_card.setFixedWidth(350)
        info_card.setFrameStyle(QFrame.NoFrame)
        info_card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:0.5 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                color: white;
            }}
        """)
        
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE, 
                                     ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE)
        info_layout.setSpacing(ModernTheme.PADDING_MEDIUM)
        
        # شعار النظام
        logo_container = self.create_integrated_logo()
        info_layout.addWidget(logo_container)
        
        # عنوان النظام
        title_label = ModernLabel("نظام Flex USA", "title")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_LARGE + 8}px;
                font-weight: bold;
                color: white;
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        info_layout.addWidget(title_label)
        
        # وصف النظام
        subtitle_label = ModernLabel("نظام المحاسبة المالي الاحترافي", "subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_NORMAL + 2}px;
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: {ModernTheme.PADDING_LARGE}px;
            }}
        """)
        info_layout.addWidget(subtitle_label)
        
        # قائمة المميزات
        features_widget = self.create_integrated_features()
        info_layout.addWidget(features_widget)
        
        # مساحة مرنة
        info_layout.addStretch()
        
        # معلومات الإصدار
        version_label = ModernLabel("الإصدار 1.0.0 - © 2024 Flex USA", "small")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
            }
        """)
        info_layout.addWidget(version_label)
        
        info_card.setLayout(info_layout)
        main_layout.addWidget(info_card)
    
    def create_integrated_logo(self):
        """إنشاء شعار متكامل"""
        logo_container = QWidget()
        logo_layout = QVBoxLayout()
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # إطار الشعار
        logo_frame = QFrame()
        logo_frame.setFixedSize(80, 80)
        logo_frame.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 40px;
            }}
        """)
        
        # أيقونة داخل الإطار
        logo_label = ModernLabel("💼", "title")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 35px;
                color: white;
                background: transparent;
                border: none;
            }
        """)
        
        frame_layout = QVBoxLayout()
        frame_layout.addWidget(logo_label)
        frame_layout.setContentsMargins(0, 0, 0, 0)
        logo_frame.setLayout(frame_layout)
        
        logo_layout.addWidget(logo_frame)
        logo_container.setLayout(logo_layout)
        
        return logo_container
    
    def create_integrated_features(self):
        """إنشاء قائمة مميزات متكاملة"""
        features_widget = QWidget()
        features_layout = QVBoxLayout()
        features_layout.setSpacing(ModernTheme.PADDING_SMALL)
        
        features = [
            ("🔒", "أمان وحماية متقدمة"),
            ("📊", "تقارير مالية شاملة"),
            ("💱", "دعم عملات متعددة"),
            ("🚀", "أداء سريع وموثوق"),
            ("☁️", "نسخ احتياطية آمنة")
        ]
        
        for icon, text in features:
            feature_widget = self.create_feature_item(icon, text)
            features_layout.addWidget(feature_widget)
        
        features_widget.setLayout(features_layout)
        return features_widget
    
    def create_feature_item(self, icon, text):
        """إنشاء عنصر مميزة واحد"""
        feature_widget = QWidget()
        feature_layout = QHBoxLayout()
        feature_layout.setContentsMargins(0, 0, 0, 0)
        feature_layout.setSpacing(ModernTheme.PADDING_SMALL)
        
        # أيقونة المميزة
        icon_label = ModernLabel(icon, "normal")
        icon_label.setFixedSize(25, 25)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: white;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }
        """)
        
        # نص المميزة
        text_label = ModernLabel(text, "normal")
        text_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernTheme.FONT_SIZE_SMALL + 1}px;
                color: rgba(255, 255, 255, 0.9);
            }}
        """)
        
        feature_layout.addWidget(icon_label)
        feature_layout.addWidget(text_label)
        feature_layout.addStretch()
        
        feature_widget.setLayout(feature_layout)
        return feature_widget

    def create_login_form_card(self, main_layout):
        """إنشاء بطاقة نموذج تسجيل الدخول"""
        login_card = QFrame()
        login_card.setFixedWidth(400)
        login_card.setFrameStyle(QFrame.NoFrame)
        login_card.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.WHITE};
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: {ModernTheme.BORDER_RADIUS}px;
            }}
        """)

        login_layout = QVBoxLayout()
        login_layout.setContentsMargins(ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE,
                                      ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE)
        login_layout.setSpacing(ModernTheme.PADDING_MEDIUM)

        # رأس تسجيل الدخول
        header_widget = self.create_login_header()
        login_layout.addWidget(header_widget)

        # نموذج تسجيل الدخول
        form_widget = self.create_integrated_form()
        login_layout.addWidget(form_widget)

        # معلومات تجريبية
        demo_info = self.create_demo_info()
        login_layout.addWidget(demo_info)

        # مساحة مرنة
        login_layout.addStretch()

        login_card.setLayout(login_layout)
        main_layout.addWidget(login_card)

    def create_login_header(self):
        """إنشاء رأس تسجيل الدخول"""
        header_widget = QWidget()
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # أيقونة تسجيل الدخول
        login_icon = ModernLabel("🔐", "title")
        login_icon.setAlignment(Qt.AlignCenter)
        login_icon.setStyleSheet(f"""
            QLabel {{
                font-size: 35px;
                color: {ModernTheme.PRIMARY_BLUE};
                background: {ModernTheme.BACKGROUND_LIGHT};
                border-radius: 30px;
                padding: {ModernTheme.PADDING_MEDIUM}px;
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        header_layout.addWidget(login_icon)

        # عنوان تسجيل الدخول
        welcome_label = ModernLabel("مرحباً بك!", "header")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.HEADER_DARK};
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        header_layout.addWidget(welcome_label)

        # وصف تسجيل الدخول
        desc_label = ModernLabel("يرجى تسجيل الدخول للوصول إلى النظام", "subtitle")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: {ModernTheme.PADDING_MEDIUM}px;
            }}
        """)
        header_layout.addWidget(desc_label)

        header_widget.setLayout(header_layout)
        return header_widget

    def create_integrated_form(self):
        """إنشاء نموذج تسجيل الدخول المتكامل"""
        form_widget = QWidget()
        form_layout = QVBoxLayout()
        form_layout.setSpacing(ModernTheme.PADDING_MEDIUM)

        # حقل اسم المستخدم باستخدام ModernInputField
        username_label = ModernLabel("اسم المستخدم:", "normal")
        form_layout.addWidget(username_label)

        self.username_input = ModernInputField("أدخل اسم المستخدم")
        form_layout.addWidget(self.username_input)

        # حقل كلمة المرور
        password_label = ModernLabel("كلمة المرور:", "normal")
        form_layout.addWidget(password_label)

        self.password_input = ModernInputField("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(self.password_input)

        # خيارات إضافية
        options_layout = self.create_login_options()
        form_layout.addLayout(options_layout)

        # أزرار العمل باستخدام ModernButton
        buttons_widget = self.create_integrated_buttons()
        form_layout.addWidget(buttons_widget)

        form_widget.setLayout(form_layout)
        return form_widget

    def create_login_options(self):
        """إنشاء خيارات تسجيل الدخول"""
        options_layout = QHBoxLayout()

        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet(f"""
            QCheckBox {{
                font-size: {ModernTheme.FONT_SIZE_SMALL}px;
                color: {ModernTheme.TEXT_SECONDARY};
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                background-color: {ModernTheme.WHITE};
            }}
            QCheckBox::indicator:checked {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
            QCheckBox::indicator:hover {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
        """)

        # رابط نسيان كلمة المرور
        forgot_link = ModernLabel('<a href="#" style="color: #3498DB; text-decoration: none;">نسيت كلمة المرور؟</a>', "small")
        forgot_link.setAlignment(Qt.AlignLeft)

        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(forgot_link)

        return options_layout

    def create_integrated_buttons(self):
        """إنشاء أزرار العمل المتكاملة"""
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # زر تسجيل الدخول باستخدام ModernButton
        self.login_button = ModernButton("تسجيل الدخول", "primary", "large")
        buttons_layout.addWidget(self.login_button)

        # زر الإلغاء
        cancel_button = ModernButton("إلغاء", "secondary", "medium")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        buttons_widget.setLayout(buttons_layout)
        return buttons_widget

    def create_demo_info(self):
        """إنشاء معلومات تسجيل الدخول التجريبية"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout()
        demo_layout.setAlignment(Qt.AlignCenter)
        demo_layout.setSpacing(ModernTheme.PADDING_SMALL)

        # عنوان المعلومات التجريبية
        demo_title = ModernLabel("معلومات تسجيل الدخول التجريبية:", "small")
        demo_title.setAlignment(Qt.AlignCenter)
        demo_title.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: {ModernTheme.PADDING_SMALL}px;
            }}
        """)
        demo_layout.addWidget(demo_title)

        # معلومات الدخول
        demo_info = ModernLabel("اسم المستخدم: admin | كلمة المرور: admin123", "small")
        demo_info.setAlignment(Qt.AlignCenter)
        demo_info.setStyleSheet(f"""
            QLabel {{
                color: {ModernTheme.TEXT_SECONDARY};
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                padding: {ModernTheme.PADDING_SMALL}px {ModernTheme.PADDING_MEDIUM}px;
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                border: 1px solid {ModernTheme.BORDER_LIGHT};
            }}
        """)
        demo_layout.addWidget(demo_info)

        demo_widget.setLayout(demo_layout)
        return demo_widget

    def create_smooth_animations(self):
        """إنشاء الحركات السلسة"""
        # تأثير الظهور التدريجي مبسط
        self.fade_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.fade_effect)

        self.fade_animation = QPropertyAnimation(self.fade_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutQuad)

        # بدء التأثير
        QTimer.singleShot(100, self.fade_animation.start)

    def connect_integrated_events(self):
        """ربط الأحداث المتكاملة"""
        self.login_button.clicked.connect(self.handle_integrated_login)
        self.password_input.returnPressed.connect(self.handle_integrated_login)
        self.username_input.textChanged.connect(self.validate_integrated_inputs)
        self.password_input.textChanged.connect(self.validate_integrated_inputs)

    def validate_integrated_inputs(self):
        """التحقق من صحة المدخلات"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # تفعيل/تعطيل زر تسجيل الدخول
        self.login_button.setEnabled(bool(username and password))

    def handle_integrated_login(self):
        """معالجة تسجيل الدخول المتكاملة"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            ModernMessageBox.show_warning(self, "تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تأثير التحميل
        original_text = self.login_button.text()
        self.login_button.setText("جاري التحقق...")
        self.login_button.setEnabled(False)

        # محاولة تسجيل الدخول
        QTimer.singleShot(800, lambda: self.process_integrated_login(username, password, original_text))

    def process_integrated_login(self, username, password, original_text):
        """معالجة عملية تسجيل الدخول"""
        try:
            from database import DatabaseManager
            db = DatabaseManager()
            user = db.authenticate_user(username, password)

            if user:
                self.user_data = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }
                ModernMessageBox.show_info(self, "نجح تسجيل الدخول", f"مرحباً {user[1]}!")
                QTimer.singleShot(1000, self.accept)
            else:
                ModernMessageBox.show_error(self, "خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()

        except Exception as e:
            ModernMessageBox.show_error(self, "خطأ في النظام", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

        finally:
            self.login_button.setText(original_text)
            self.login_button.setEnabled(True)

    def get_user_data(self):
        """الحصول على بيانات المستخدم"""
        return self.user_data

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

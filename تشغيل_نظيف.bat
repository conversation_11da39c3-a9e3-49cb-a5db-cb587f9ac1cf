@echo off
chcp 65001 >nul
title Flex USA Accounting System - تشغيل نظيف

echo.
echo ================================================
echo    🚀 نظام Flex USA المحاسبي - الإصدار النظيف
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من المكتبات المطلوبة
echo 🔍 فحص المكتبات المطلوبة...
python -c "import PyQt5" 2>nul
if errorlevel 1 (
    echo ❌ PyQt5 غير مثبت
    echo 🔧 جاري تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات متوفرة

REM تشغيل النظام
echo.
echo 🎯 تشغيل النظام...
echo.
python run_clean.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo 💡 تحقق من رسائل الخطأ أعلاه
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
)

echo.
pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام Flex USA المحاسبي
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        'PyQt5',
        'reportlab',
        'arabic_reshaper',
        'bidi'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"   • {module}")
        print("\n💡 لتثبيت المكتبات المفقودة:")
        print("pip install PyQt5 reportlab arabic-reshaper python-bidi")
        return False
    
    return True

def setup_application():
    """إعداد التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont()
    font.setFamily("Segoe UI")
    font.setPointSize(12)
    app.setFont(font)
    
    return app

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'assets',
        'assets/fonts',
        'backups',
        'reports',
        'invoices',
        'temp'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"✅ تم إنشاء مجلد: {directory}")
            except Exception as e:
                print(f"❌ فشل في إنشاء مجلد {directory}: {e}")

def show_error_dialog(title, message):
    """عرض رسالة خطأ"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    msg_box = QMessageBox()
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setLayoutDirection(Qt.RightToLeft)
    msg_box.exec_()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام Flex USA المحاسبي")
    print("=" * 50)
    
    # فحص المكتبات المطلوبة
    print("🔍 فحص المكتبات المطلوبة...")
    if not check_dependencies():
        return 1
    print("✅ جميع المكتبات متوفرة")
    
    # إنشاء المجلدات المطلوبة
    print("\n📁 إنشاء المجلدات المطلوبة...")
    create_directories()
    
    try:
        # إعداد التطبيق
        print("\n⚙️ إعداد التطبيق...")
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")
        
        # استيراد النافذة الرئيسية
        print("\n📦 تحميل النافذة الرئيسية...")
        from main_window import MainWindow
        print("✅ تم تحميل النافذة الرئيسية")
        
        # إنشاء النافذة الرئيسية
        print("\n🏠 إنشاء النافذة الرئيسية...")
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # محاولة تسجيل الدخول
        print("\n🔐 فتح نافذة تسجيل الدخول...")
        if main_window.login():
            print("✅ تم تسجيل الدخول بنجاح")
            main_window.show()
            
            print("\n🎉 النظام جاهز للاستخدام!")
            print("💡 معلومات تسجيل الدخول الافتراضية:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("\n🔥 استمتع بالنظام الموحد الجديد!")
            
            # تشغيل التطبيق
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
    except ImportError as e:
        error_msg = f"خطأ في استيراد الملفات:\n{str(e)}\n\nتأكد من وجود جميع ملفات النظام"
        print(f"❌ {error_msg}")
        show_error_dialog("خطأ في الاستيراد", error_msg)
        return 1
        
    except Exception as e:
        error_msg = f"حدث خطأ غير متوقع:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        print(f"❌ {error_msg}")
        show_error_dialog("خطأ غير متوقع", error_msg)
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف النظام بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n\n💥 خطأ فادح: {e}")
        traceback.print_exc()
        sys.exit(1)

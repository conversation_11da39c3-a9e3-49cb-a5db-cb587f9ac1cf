#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة تسجيل الدخول المتكاملة لنظام Flex USA المحاسبي
متوافقة تماماً مع modern_components و ModernTheme
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_theme import ModernTheme
from modern_components import *
from icon_manager import icon_manager

class ModernLoginDialog(ModernDialog):
    """شاشة تسجيل دخول متكاملة مع النظام"""

    def __init__(self, parent=None):
        super().__init__("🔐 تسجيل الدخول - Flex USA", parent)
        self.user_data = None
        self.setup_integrated_login()
        self.create_smooth_animations()
    
    def setup_integrated_login(self):
        """إعداد شاشة تسجيل الدخول المتكاملة"""
        # إعدادات النافذة متوافقة مع ModernDialog
        self.setFixedSize(800, 550)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأيقونة الموحدة
        icon_manager.apply_to_window(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE,
                                     ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE)
        main_layout.setSpacing(ModernTheme.PADDING_LARGE)

        # إنشاء الجانب الأيمن (معلومات النظام)
        self.create_system_info_card(main_layout)

        # إنشاء الجانب الأيسر (نموذج تسجيل الدخول)
        self.create_login_form_card(main_layout)

        self.setLayout(main_layout)

        # ربط الأحداث
        self.connect_integrated_events()

        # تركيز على حقل اسم المستخدم
        QTimer.singleShot(100, lambda: self.username_input.setFocus())
    
    def create_system_info_card(self, main_layout):
        """إنشاء بطاقة معلومات النظام باستخدام ModernCard"""
        info_card = ModernCard()
        info_card.setFixedWidth(350)
        info_card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:0.5 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
                border: none;
                border-radius: {ModernTheme.BORDER_RADIUS}px;
                color: white;
            }}
        """)

        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE,
                                     ModernTheme.PADDING_LARGE, ModernTheme.PADDING_LARGE)
        info_layout.setSpacing(ModernTheme.PADDING_MEDIUM)
        
        # شعار النظام مع تأثير بصري
        logo_container = self.create_logo_section()
        info_layout.addWidget(logo_container)
        
        # عنوان النظام
        title_label = QLabel("نظام Flex USA")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 42px;
                font-weight: bold;
                color: white;
                margin-bottom: 15px;

            }
        """)
        info_layout.addWidget(title_label)
        
        # وصف النظام
        subtitle_label = QLabel("نظام المحاسبة المالي الاحترافي")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: 40px;
                font-weight: 300;
            }
        """)
        info_layout.addWidget(subtitle_label)
        
        # قائمة المميزات
        features_widget = self.create_features_list()
        info_layout.addWidget(features_widget)
        
        # مساحة مرنة
        info_layout.addStretch()
        
        # معلومات الإصدار
        version_container = self.create_version_info()
        info_layout.addWidget(version_container)
        
        info_card.setLayout(info_layout)
        main_layout.addWidget(info_card)
    
    def create_logo_section(self):
        """إنشاء قسم الشعار مع تأثيرات بصرية"""
        logo_container = QWidget()
        logo_layout = QVBoxLayout()
        logo_layout.setAlignment(Qt.AlignCenter)
        
        # إطار الشعار مع تأثير الظل
        logo_frame = QFrame()
        logo_frame.setFixedSize(120, 120)
        logo_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(255, 255, 255, 0.1));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 60px;
            }}
        """)
        
        # أيقونة داخل الإطار
        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 50px;
                color: white;
                background: transparent;
                border: none;
            }
        """)
        
        frame_layout = QVBoxLayout()
        frame_layout.addWidget(logo_label)
        frame_layout.setContentsMargins(0, 0, 0, 0)
        logo_frame.setLayout(frame_layout)
        
        logo_layout.addWidget(logo_frame)
        logo_container.setLayout(logo_layout)
        
        return logo_container
    
    def create_features_list(self):
        """إنشاء قائمة مميزات النظام"""
        features_widget = QWidget()
        features_layout = QVBoxLayout()
        features_layout.setSpacing(20)
        
        features = [
            ("🔒", "أمان وحماية متقدمة", "تشفير البيانات وحماية المعلومات"),
            ("📊", "تقارير مالية شاملة", "تحليلات مفصلة وإحصائيات دقيقة"),
            ("💱", "دعم عملات متعددة", "تحويل العملات وأسعار الصرف"),
            ("🚀", "أداء سريع وموثوق", "معالجة فورية للعمليات المالية"),
            ("☁️", "نسخ احتياطية آمنة", "حفظ تلقائي واسترجاع البيانات")
        ]
        
        for icon, title, desc in features:
            feature_widget = self.create_feature_item(icon, title, desc)
            features_layout.addWidget(feature_widget)
        
        features_widget.setLayout(features_layout)
        return features_widget
    
    def create_feature_item(self, icon, title, description):
        """إنشاء عنصر مميزة واحد"""
        feature_widget = QWidget()
        feature_layout = QHBoxLayout()
        feature_layout.setContentsMargins(0, 0, 0, 0)
        feature_layout.setSpacing(15)
        
        # أيقونة المميزة
        icon_label = QLabel(icon)
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: white;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
            }
        """)
        
        # نص المميزة
        text_container = QWidget()
        text_layout = QVBoxLayout()
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: white;
            }
        """)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.7);
            }
        """)
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        text_container.setLayout(text_layout)
        
        feature_layout.addWidget(icon_label)
        feature_layout.addWidget(text_container)
        feature_layout.addStretch()
        
        feature_widget.setLayout(feature_layout)
        return feature_widget
    
    def create_version_info(self):
        """إنشاء معلومات الإصدار"""
        version_container = QWidget()
        version_layout = QVBoxLayout()
        version_layout.setAlignment(Qt.AlignCenter)
        
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                margin-bottom: 5px;
            }
        """)
        
        copyright_label = QLabel("© 2024 Flex USA - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.5);
            }
        """)
        
        version_layout.addWidget(version_label)
        version_layout.addWidget(copyright_label)
        version_container.setLayout(version_layout)
        
        return version_container

    def create_login_panel(self, main_layout):
        """إنشاء لوحة تسجيل الدخول"""
        login_widget = QWidget()
        login_widget.setFixedWidth(450)
        login_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernTheme.WHITE};
                border-top-left-radius: 20px;
                border-bottom-left-radius: 20px;
            }}
        """)

        login_layout = QVBoxLayout()
        login_layout.setContentsMargins(60, 40, 60, 40)
        login_layout.setSpacing(25)

        # زر الإغلاق
        close_button = self.create_close_button()
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        login_layout.addLayout(close_layout)

        # مساحة مرنة
        login_layout.addStretch()

        # رأس تسجيل الدخول
        header_widget = self.create_login_header()
        login_layout.addWidget(header_widget)

        # نموذج تسجيل الدخول
        form_widget = self.create_login_form()
        login_layout.addWidget(form_widget)

        # معلومات تجريبية
        demo_info = self.create_demo_info()
        login_layout.addWidget(demo_info)

        # مساحة مرنة
        login_layout.addStretch()

        login_widget.setLayout(login_layout)
        main_layout.addWidget(login_widget)

    def create_close_button(self):
        """إنشاء زر الإغلاق"""
        close_button = QPushButton("✕")
        close_button.setFixedSize(35, 35)
        close_button.clicked.connect(self.reject)
        close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: none;
                border-radius: 17px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.WARNING_RED};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.WARNING_HOVER};
            }}
        """)
        return close_button

    def create_login_header(self):
        """إنشاء رأس تسجيل الدخول"""
        header_widget = QWidget()
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)

        # أيقونة تسجيل الدخول
        login_icon = QLabel("🔐")
        login_icon.setAlignment(Qt.AlignCenter)
        login_icon.setStyleSheet(f"""
            QLabel {{
                font-size: 40px;
                color: {ModernTheme.PRIMARY_BLUE};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernTheme.BACKGROUND_LIGHT},
                    stop:1 {ModernTheme.WHITE});
                border-radius: 35px;
                padding: 15px;
                margin-bottom: 10px;
            }}
        """)
        header_layout.addWidget(login_icon)

        # عنوان تسجيل الدخول
        welcome_label = QLabel("مرحباً بك!")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {ModernTheme.HEADER_DARK};
                margin-bottom: 5px;
            }}
        """)
        header_layout.addWidget(welcome_label)

        # وصف تسجيل الدخول
        desc_label = QLabel("يرجى تسجيل الدخول للوصول إلى النظام")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: 20px;
            }}
        """)
        header_layout.addWidget(desc_label)

        header_widget.setLayout(header_layout)
        return header_widget

    def create_login_form(self):
        """إنشاء نموذج تسجيل الدخول"""
        form_widget = QWidget()
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)

        # حقل اسم المستخدم
        username_container = self.create_input_field(
            "👤", "اسم المستخدم", "أدخل اسم المستخدم"
        )
        self.username_input = username_container.findChild(QLineEdit)
        form_layout.addWidget(username_container)

        # حقل كلمة المرور
        password_container = self.create_input_field(
            "🔒", "كلمة المرور", "أدخل كلمة المرور", is_password=True
        )
        self.password_input = password_container.findChild(QLineEdit)
        form_layout.addWidget(password_container)

        # خيارات إضافية
        options_layout = self.create_login_options()
        form_layout.addLayout(options_layout)

        # أزرار العمل
        buttons_widget = self.create_action_buttons()
        form_layout.addWidget(buttons_widget)

        form_widget.setLayout(form_layout)
        return form_widget

    def create_input_field(self, icon, label, placeholder, is_password=False):
        """إنشاء حقل إدخال مع أيقونة وتسمية"""
        container = QWidget()
        container_layout = QVBoxLayout()
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(8)

        # تسمية الحقل
        field_label = QLabel(label)
        field_label.setStyleSheet(f"""
            QLabel {{
                font-size: 13px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY};
                margin-bottom: 5px;
            }}
        """)
        container_layout.addWidget(field_label)

        # حاوية الحقل مع الأيقونة
        input_container = QWidget()
        input_container.setFixedHeight(50)
        input_container.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernTheme.WHITE};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 10px;
            }}
            QWidget:hover {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
        """)

        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(15, 0, 15, 0)
        input_layout.setSpacing(10)

        # أيقونة الحقل
        icon_label = QLabel(icon)
        icon_label.setFixedSize(20, 20)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)
        input_layout.addWidget(icon_label)

        # حقل الإدخال
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background: transparent;
                border: none;
                font-size: 14px;
                color: {ModernTheme.TEXT_PRIMARY};
                padding: 0px;
            }}
            QLineEdit::placeholder {{
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)

        if is_password:
            input_field.setEchoMode(QLineEdit.Password)

        input_layout.addWidget(input_field)
        input_container.setLayout(input_layout)
        container_layout.addWidget(input_container)

        container.setLayout(container_layout)
        return container

    def create_login_options(self):
        """إنشاء خيارات تسجيل الدخول"""
        options_layout = QHBoxLayout()

        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet(f"""
            QCheckBox {{
                font-size: 12px;
                color: {ModernTheme.TEXT_SECONDARY};
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                background-color: {ModernTheme.WHITE};
            }}
            QCheckBox::indicator:checked {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                border-color: {ModernTheme.PRIMARY_BLUE};
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }}
            QCheckBox::indicator:hover {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}
        """)

        # رابط نسيان كلمة المرور
        forgot_link = QLabel('<a href="#" style="color: #3498DB; text-decoration: none;">نسيت كلمة المرور؟</a>')
        forgot_link.setAlignment(Qt.AlignLeft)
        forgot_link.setStyleSheet("""
            QLabel {
                font-size: 12px;
            }
            QLabel a:hover {
                text-decoration: underline;
            }
        """)

        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(forgot_link)

        return options_layout

    def create_action_buttons(self):
        """إنشاء أزرار العمل"""
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFixedHeight(50)
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_BLUE},
                    stop:1 {ModernTheme.PRIMARY_HOVER});
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernTheme.PRIMARY_HOVER},
                    stop:1 {ModernTheme.HEADER_DARK});
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.HEADER_DARK};
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.SECONDARY_GRAY};
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)
        buttons_layout.addWidget(self.login_button)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFixedHeight(45)
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {ModernTheme.TEXT_SECONDARY};
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 22px;
                font-size: 14px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                border-color: {ModernTheme.SECONDARY_GRAY};
                color: {ModernTheme.TEXT_PRIMARY};
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.SECONDARY_GRAY};
            }}
        """)
        buttons_layout.addWidget(cancel_button)

        buttons_widget.setLayout(buttons_layout)
        return buttons_widget

    def create_demo_info(self):
        """إنشاء معلومات تسجيل الدخول التجريبية"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout()
        demo_layout.setAlignment(Qt.AlignCenter)
        demo_layout.setSpacing(8)

        # عنوان المعلومات التجريبية
        demo_title = QLabel("معلومات تسجيل الدخول التجريبية:")
        demo_title.setAlignment(Qt.AlignCenter)
        demo_title.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: 600;
                color: {ModernTheme.TEXT_SECONDARY};
                margin-bottom: 5px;
            }}
        """)
        demo_layout.addWidget(demo_title)

        # معلومات الدخول
        demo_info = QLabel("اسم المستخدم: admin | كلمة المرور: admin123")
        demo_info.setAlignment(Qt.AlignCenter)
        demo_info.setStyleSheet(f"""
            QLabel {{
                font-size: 11px;
                color: {ModernTheme.TEXT_SECONDARY};
                background-color: {ModernTheme.BACKGROUND_LIGHT};
                padding: 8px 15px;
                border-radius: 15px;
                border: 1px solid {ModernTheme.BORDER_LIGHT};
            }}
        """)
        demo_layout.addWidget(demo_info)

        demo_widget.setLayout(demo_layout)
        return demo_widget

    def create_animations(self):
        """إنشاء الحركات والتأثيرات"""
        # تأثير الظهور التدريجي
        self.fade_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.fade_effect)

        self.fade_animation = QPropertyAnimation(self.fade_effect, b"opacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # بدء التأثير
        QTimer.singleShot(50, self.fade_animation.start)

    def connect_events(self):
        """ربط الأحداث"""
        self.login_button.clicked.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.textChanged.connect(self.validate_inputs)
        self.password_input.textChanged.connect(self.validate_inputs)

        # تأثيرات تفاعلية للحقول
        self.username_input.focusInEvent = lambda _: self.on_field_focus(self.username_input.parent(), True)
        self.username_input.focusOutEvent = lambda _: self.on_field_focus(self.username_input.parent(), False)
        self.password_input.focusInEvent = lambda _: self.on_field_focus(self.password_input.parent(), True)
        self.password_input.focusOutEvent = lambda _: self.on_field_focus(self.password_input.parent(), False)

    def on_field_focus(self, container, has_focus):
        """تأثير التركيز على الحقول"""
        if has_focus:
            container.setStyleSheet(f"""
                QWidget {{
                    background-color: {ModernTheme.WHITE};
                    border: 2px solid {ModernTheme.PRIMARY_BLUE};
                    border-radius: 10px;
                }}
            """)
        else:
            container.setStyleSheet(f"""
                QWidget {{
                    background-color: {ModernTheme.WHITE};
                    border: 2px solid {ModernTheme.BORDER_LIGHT};
                    border-radius: 10px;
                }}
                QWidget:hover {{
                    border-color: {ModernTheme.PRIMARY_BLUE};
                }}
            """)

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # تفعيل/تعطيل زر تسجيل الدخول
        self.login_button.setEnabled(bool(username and password))

        # تغيير لون الزر حسب الحالة
        if username and password:
            self.login_button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernTheme.PRIMARY_BLUE},
                        stop:1 {ModernTheme.PRIMARY_HOVER});
                    color: white;
                    border: none;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernTheme.PRIMARY_HOVER},
                        stop:1 {ModernTheme.HEADER_DARK});
                }}
            """)

    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            self.show_message("تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور", "warning")
            return

        # تأثير التحميل
        self.login_button.setText("جاري التحقق...")
        self.login_button.setEnabled(False)

        # محاولة تسجيل الدخول
        QTimer.singleShot(1000, lambda: self.process_login(username, password))

    def process_login(self, username, password):
        """معالجة عملية تسجيل الدخول"""
        try:
            from database import DatabaseManager
            db = DatabaseManager()
            user = db.authenticate_user(username, password)

            if user:
                self.user_data = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2]
                }
                self.show_message("نجح تسجيل الدخول", f"مرحباً {user[1]}!", "success")
                QTimer.singleShot(1500, self.accept)
            else:
                self.show_message("خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة", "error")
                self.password_input.clear()
                self.password_input.setFocus()

        except Exception as e:
            self.show_message("خطأ في النظام", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}", "error")

        finally:
            self.login_button.setText("تسجيل الدخول")
            self.login_button.setEnabled(True)

    def show_message(self, title, message, msg_type="info"):
        """عرض رسالة تنبيه احترافية"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأيقونة
        icon_manager.apply_to_window(msg)

        # تحديد نوع الرسالة
        if msg_type == "success":
            msg.setIcon(QMessageBox.Information)
        elif msg_type == "warning":
            msg.setIcon(QMessageBox.Warning)
        elif msg_type == "error":
            msg.setIcon(QMessageBox.Critical)
        else:
            msg.setIcon(QMessageBox.Information)

        # تطبيق ستايل احترافي
        msg.setStyleSheet(f"""
            QMessageBox {{
                background-color: {ModernTheme.WHITE};
                color: {ModernTheme.TEXT_PRIMARY};
                font-size: 14px;
                border-radius: 10px;
            }}
            QMessageBox QPushButton {{
                background-color: {ModernTheme.PRIMARY_BLUE};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-weight: 600;
                min-width: 80px;
                min-height: 30px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {ModernTheme.PRIMARY_HOVER};
            }}
        """)

        msg.exec_()

    def get_user_data(self):
        """الحصول على بيانات المستخدم"""
        return self.user_data

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام Flex USA المحاسبي - الإصدار المحدث
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 تشغيل نظام Flex USA المحاسبي")
        print("🔐 معلومات تسجيل الدخول: admin / admin123")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont

        # إنشاء التطبيق أولاً
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # إعداد الخط
        font = QFont("Segoe UI", 12)
        app.setFont(font)

        # إنشاء المجلدات
        for directory in ['assets', 'backups', 'reports', 'invoices', 'temp']:
            if not os.path.exists(directory):
                os.makedirs(directory)

        # الآن يمكن استيراد النافذة الرئيسية بأمان
        from main_window import MainWindow

        # إنشاء النافذة الرئيسية
        main_window = MainWindow()

        # تسجيل الدخول
        if main_window.login():
            main_window.show()
            print("✅ تم تسجيل الدخول بنجاح!")
            return app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0

    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

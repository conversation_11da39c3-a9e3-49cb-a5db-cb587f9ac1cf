#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أنماط CSS لنظام Flex USA المحاسبي
"""

class LoginStyles:
    """أنماط شاشة تسجيل الدخول"""

    # الخلفية الرئيسية
    MAIN_BACKGROUND = """
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 15px;
        }
    """
    
    # قسم الرأس
    HEADER_SECTION = """
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255,255,255,0.1), stop:1 rgba(255,255,255,0.05));
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
        }
    """
    
    # شعار النظام
    LOGO_STYLE = """
        QLabel {
            font-size: 64px;
            color: white;
            background: transparent;
            margin: 15px;
        }
    """
    
    # عنوان النظام
    TITLE_STYLE = """
        QLabel {
            font-size: 28px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
    """
    
    # النص الفرعي
    SUBTITLE_STYLE = """
        QLabel {
            font-size: 14px;
            color: rgba(255,255,255,0.9);
            background: transparent;
            margin: 5px;
        }
    """
    
    # قسم النموذج
    FORM_SECTION = """
        QWidget {
            background: rgba(255,255,255,0.95);
            border: none;
        }
    """
    
    # عنوان تسجيل الدخول
    LOGIN_TITLE = """
        QLabel {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }
    """
    
    # أيقونة حقل الإدخال
    INPUT_ICON = """
        QLabel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            border-top-left-radius: 27px;
            border-bottom-left-radius: 27px;
            font-size: 18px;
            color: white;
        }
    """
    
    # حقل الإدخال
    INPUT_FIELD = """
        QLineEdit {
            border: 2px solid #e0e0e0;
            border-left: none;
            border-top-right-radius: 27px;
            border-bottom-right-radius: 27px;
            padding: 0 20px;
            font-size: 14px;
            background: white;
            color: #2c3e50;
        }
        QLineEdit:focus {
            border-color: #667eea;
            outline: none;
        }
    """
    
    # خانة الاختيار
    CHECKBOX_STYLE = """
        QCheckBox {
            color: #7f8c8d;
            font-size: 13px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background: white;
        }
        QCheckBox::indicator:checked {
            border: 2px solid #667eea;
            border-radius: 4px;
            background: #667eea;
        }
    """
    
    # الزر الأساسي
    PRIMARY_BUTTON = """
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 15px;
            font-weight: bold;
            padding: 10px 25px;
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #7c8df0, stop:1 #8a5fb8);
        }
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a6fd8, stop:1 #6b4190);
        }
        QPushButton:disabled {
            background: #bdc3c7;
            color: #7f8c8d;
        }
    """
    
    # الزر الثانوي
    SECONDARY_BUTTON = """
        QPushButton {
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 15px;
            font-weight: bold;
            padding: 10px 25px;
        }
        QPushButton:hover {
            background: #7f8c8d;
        }
        QPushButton:pressed {
            background: #6c7b7d;
        }
    """
    
    # قسم التذييل
    FOOTER_SECTION = """
        QWidget {
            background: rgba(255,255,255,0.1);
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
        }
    """
    
    # نص الإصدار
    VERSION_TEXT = """
        QLabel {
            color: rgba(255,255,255,0.8);
            font-size: 13px;
            background: transparent;
        }
    """
    
    # نص حقوق الطبع
    COPYRIGHT_TEXT = """
        QLabel {
            color: rgba(255,255,255,0.6);
            font-size: 11px;
            background: transparent;
        }
    """

class MainWindowStyles:
    """أنماط النافذة الرئيسية"""
    
    # النافذة الرئيسية
    MAIN_WINDOW = """
        QMainWindow {
            background: #f8f9fa;
            color: #2c3e50;
        }
    """
    
    # شريط القوائم
    MENU_BAR = """
        QMenuBar {
            background: #667eea;
            color: white;
            border: none;
            padding: 5px;
        }
        QMenuBar::item {
            background: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }
        QMenuBar::item:selected {
            background: rgba(255,255,255,0.2);
        }
        QMenu {
            background: white;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
        }
        QMenu::item {
            padding: 8px 20px;
        }
        QMenu::item:selected {
            background: #667eea;
            color: white;
        }
    """
    
    # شريط الأدوات
    TOOLBAR = """
        QToolBar {
            background: white;
            border: none;
            border-bottom: 1px solid #e0e0e0;
            padding: 5px;
        }
        QToolBar QToolButton {
            background: transparent;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            color: #2c3e50;
        }
        QToolBar QToolButton:hover {
            background: #667eea;
            color: white;
        }
    """
    
    # مجموعة العناصر
    GROUP_BOX = """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin: 10px 0;
            padding-top: 15px;
            background: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #667eea;
            font-size: 14px;
        }
    """
    
    # الجدول
    TABLE_WIDGET = """
        QTableWidget {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            gridline-color: #f0f0f0;
            selection-background-color: #667eea;
        }
        QTableWidget::item {
            padding: 8px;
            border: none;
        }
        QTableWidget::item:selected {
            background: #667eea;
            color: white;
        }
        QHeaderView::section {
            background: #f8f9fa;
            color: #2c3e50;
            padding: 10px;
            border: none;
            border-bottom: 2px solid #667eea;
            font-weight: bold;
        }
    """
    
    # بطاقة العملة
    CURRENCY_CARD = """
        QGroupBox {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin: 5px;
            padding: 15px;
            font-weight: bold;
            color: #2c3e50;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 10px;
            color: #667eea;
            font-size: 16px;
        }
    """

class DialogStyles:
    """أنماط النوافذ المنبثقة"""
    
    # النافذة المنبثقة
    DIALOG = """
        QDialog {
            background: white;
            border-radius: 10px;
        }
    """
    
    # رسالة التأكيد
    MESSAGE_BOX = """
        QMessageBox {
            background: white;
            color: #2c3e50;
        }
        QMessageBox QPushButton {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 80px;
        }
        QMessageBox QPushButton:hover {
            background: #764ba2;
        }
    """
    
    # رسالة الخطأ
    ERROR_MESSAGE_BOX = """
        QMessageBox {
            background: white;
            color: #2c3e50;
        }
        QMessageBox QPushButton {
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 80px;
        }
        QMessageBox QPushButton:hover {
            background: #c0392b;
        }
    """

class RTLStyles:
    """أنماط خاصة بدعم اللغة العربية من اليمين إلى اليسار"""

    # أنماط عامة للنصوص العربية
    ARABIC_TEXT = """
        * {
            font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
            direction: rtl;
        }
        QLabel {
            text-align: right;
            qproperty-alignment: AlignRight;
        }
        QLineEdit {
            text-align: right;
            qproperty-alignment: AlignRight;
        }
        QTextEdit {
            text-align: right;
            qproperty-alignment: AlignRight;
        }
        QComboBox {
            text-align: right;
        }
    """

    # أنماط الجداول مع دعم RTL
    TABLE_RTL = """
        QTableWidget {
            qproperty-layoutDirection: RightToLeft;
        }
        QHeaderView::section {
            text-align: center;
        }
        QTableWidget::item {
            text-align: right;
            padding-right: 10px;
        }
    """

    # أنماط القوائم مع دعم RTL
    MENU_RTL = """
        QMenuBar {
            qproperty-layoutDirection: RightToLeft;
        }
        QMenu {
            qproperty-layoutDirection: RightToLeft;
        }
        QMenu::item {
            text-align: right;
            padding-right: 20px;
        }
    """

    # أنماط الأزرار مع دعم RTL
    BUTTON_RTL = """
        QPushButton {
            text-align: center;
        }
        QToolButton {
            text-align: center;
        }
    """

    # أنماط النماذج مع دعم RTL
    FORM_RTL = """
        QFormLayout {
            qproperty-labelAlignment: AlignRight;
            qproperty-formAlignment: AlignRight;
        }
        QGroupBox {
            qproperty-alignment: AlignRight;
        }
        QGroupBox::title {
            text-align: right;
            padding-right: 10px;
        }
    """
